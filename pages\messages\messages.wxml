<!--messages.wxml-->
<view class="container page-bottom-safe-area">

  <!-- 主要tab标签 -->
  <view class="main-tabs-wrapper">
    <view class="main-tabs">
      <view class="main-tab {{currentMainTab === item.key ? 'active' : ''}}"
            wx:for="{{mainTabs}}"
            wx:key="key"
            bindtap="switchMainTab"
            data-tab="{{item.key}}">
        <text class="tab-text">{{item.name}}</text>
        <!-- 未读数量角标 -->
        <view class="unread-badge" wx:if="{{unreadCounts[item.key] > 0}}">
          <text class="unread-count">{{unreadCounts[item.key] > 99 ? '99+' : unreadCounts[item.key]}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 消息内容区 -->
  <view class="message-content list-container">
    <!-- 系统消息列表 -->
    <view class="message-panel active" wx:if="{{currentMainTab === 'system_message'}}">
      <!-- 加载状态 -->
      <view class="messages-loading" wx:if="{{systemLoading}}">
        <view class="loading-text">正在加载系统消息...</view>
      </view>
      
      <!-- 有消息时显示列表 -->
      <view wx:elif="{{systemMessages && systemMessages.length > 0}}">
        <view class="message-item"
              wx:for="{{systemMessages}}"
              wx:key="id"
              bindtap="showMessageDetail"
              data-message="{{item}}"
              data-type="system_message">
          <view class="message-icon">
            <icon class="iconfont icon-system" type="info"></icon>
          </view>
          <view class="message-info">
            <view class="message-title">{{item.title}}</view>
            <rich-text>{{item.content}}</rich-text>
            <view class="message-time">{{item.time}}</view>
          </view>
          <!-- 未读状态红点指示器 - 只在未读时显示 -->
          <view class="message-status unread" wx:if="{{item.status !== 'READ'}}"></view>
        </view>
        
        <!-- 加载更多提示 -->
        <view class="load-more" wx:if="{{systemHasMore}}">
          <view class="load-more-text">上拉加载更多</view>
        </view>
        
        <!-- 没有更多数据提示 -->
        <view class="no-more" wx:else>
          <view class="no-more-text">没有更多消息了</view>
        </view>
      </view>
      
      <!-- 无消息时显示空状态 -->
      <view class="empty-messages" wx:else>
        <view class="empty-icon">📭</view>
        <view class="empty-text">暂无系统消息</view>
      </view>
    </view>

    <!-- 通知公告列表 -->
    <view class="message-panel active" wx:if="{{currentMainTab === 'notice_announcement'}}">
      <!-- 加载状态 -->
      <view class="messages-loading" wx:if="{{noticeLoading}}">
        <view class="loading-text">正在加载通知公告...</view>
      </view>
      
      <!-- 有消息时显示列表 -->
      <view wx:elif="{{noticeMessages && noticeMessages.length > 0}}">
        <view class="message-item {{item.type === 'emergency_notice' ? 'emergency' : ''}}"
              wx:for="{{noticeMessages}}"
              wx:key="id"
              bindtap="showMessageDetail"
              data-message="{{item}}"
              data-type="notice_announcement">
          <view class="message-icon">
            <icon class="iconfont " type="info"></icon>
          </view>
          <view class="message-info">
            <view class="message-title">{{item.title}}</view>
            <!-- <rich-text class="message-time">{{item.content}}</rich-text> -->
            <view class="message-time">{{item.time}}</view>
          </view>
          <!-- 未读状态红点指示器 - 只在未读时显示 -->
          <view class="message-status unread" wx:if="{{!item.read}}"></view>
        </view>
        
        <!-- 加载更多提示 -->
        <view class="load-more" wx:if="{{noticeHasMore}}">
          <view class="load-more-text">上拉加载更多</view>
        </view>
        
        <!-- 没有更多数据提示 -->
        <view class="no-more" wx:else>
          <view class="no-more-text">没有更多消息了</view>
        </view>
      </view>
      
      <!-- 无消息时显示空状态 -->
      <view class="empty-messages" wx:else>
        <view class="empty-icon">📭</view>
        <view class="empty-text">暂无通知公告</view>
      </view>
    </view>

    <!-- 站内私信列表 -->
    <view class="message-panel active" wx:if="{{currentMainTab === 'private_message'}}">
      <!-- 加载状态 -->
      <view class="messages-loading" wx:if="{{privateLoading}}">
        <view class="loading-text">正在加载站内私信...</view>
      </view>
      
      <!-- 有消息时显示列表 -->
      <view wx:elif="{{privateMessages && privateMessages.length > 0}}">
        <view class="message-item private-message"
              wx:for="{{privateMessages}}"
              wx:key="userId"
              bindtap="showMessageDetail"
              data-message="{{item}}"
              data-type="private_message">
          <view class="message-avatar">
            <image class="avatar-img" src="{{item.avatarUrl ? (apiUrl + item.avatarUrl) : '/images/default-avatar.svg'}}" mode="aspectFill"></image>
          </view>
          <view class="message-info">
            <view class="message-title">{{item.userName}}</view>
            <view class="message-content-preview">{{item.type=='text'?item.content:'[图片]'}}</view>
            <view class="message-time">{{item.time}}</view>
          </view>
          <!-- 未读数量显示 -->
          <view class="message-unread-count" wx:if="{{item.countUnread > 0}}">
            <text class="unread-count-text">{{item.countUnread > 99 ? '99+' : item.countUnread}}</text>
          </view>
        </view>
        
        <!-- 加载更多提示 -->
        <view class="load-more" wx:if="{{privateHasMore}}">
          <view class="load-more-text">上拉加载更多</view>
        </view>
        
        <!-- 没有更多数据提示 -->
        <view class="no-more" wx:else>
          <view class="no-more-text">没有更多消息了</view>
        </view>
      </view>
      
      <!-- 无消息时显示空状态 -->
      <view class="empty-messages" wx:else>
        <view class="empty-icon">📭</view>
        <view class="empty-text">暂无站内私信</view>
      </view>
    </view>
  </view>
</view>

<!-- 消息详情弹窗 -->
<view class="message-modal" wx:if="{{showMessageModal}}" bindtap="hideMessageModal">
  <view class="message-modal-content" catchtap="stopPropagation">
    <!-- 弹窗头部 -->
    <view class="modal-header">
      <view class="modal-title">{{currentMessage.title || '消息详情'}}</view>
      <view class="modal-close" bindtap="hideMessageModal">
        <text class="close-icon">×</text>
      </view>
    </view>

    <!-- 弹窗内容 -->
    <view class="modal-body">
      <view class="modal-time">{{currentMessage.time}}</view>
      <rich-text class="modal-content">{{currentMessage.content || '暂无内容'}}</rich-text>
    </view>
  </view>
</view>
