<!--员工详情页面-->
<view class="container {{darkMode ? 'darkMode' : ''}}">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 员工详情 -->
  <block wx:if="{{!isLoading && staffInfo}}">
    <!-- 顶部信息卡片 -->
    <view class="staff-header">
      <view class="staff-basic-info">
        <view class="staff-name-id">
          <text class="staff-name">{{staffInfo.name}}</text>
          <text class="staff-status {{staffInfo.status === '在职' ? 'active' : 'inactive'}}">{{staffInfo.status}}</text>
        </view>
        <view class="staff-position">{{staffInfo.organization}} | {{staffInfo.position}}</view>
        <view class="staff-id">员工编号：{{staffInfo.employeeId}}</view>
      </view>
      <view class="action-btn" bindtap="showActions">
        <view class="action-icon"></view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="quick-actions">
      <view class="action-item" bindtap="callPhone">
        <view class="action-icon-call"></view>
        <text>拨打电话</text>
      </view>
      <view class="action-item" bindtap="sendMessage">
        <view class="action-icon-message"></view>
        <text>发送短信</text>
      </view>
      <view class="action-item" bindtap="editStaff">
        <view class="action-icon-edit"></view>
        <text>编辑信息</text>
      </view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="section-title">
        <view class="section-icon-basic"></view>
        <text>基本信息</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">姓名</text>
          <text class="info-value">{{staffInfo.name}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">性别</text>
          <text class="info-value">{{staffInfo.gender}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">年龄</text>
          <text class="info-value">{{staffInfo.age}}岁</text>
        </view>
        <view class="info-item">
          <text class="info-label">手机号码</text>
          <text class="info-value">{{staffInfo.phone}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">身份证号</text>
          <text class="info-value sensitive">{{staffInfo.idCard}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">邮箱</text>
          <text class="info-value">{{staffInfo.email || '未设置'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">住址</text>
          <text class="info-value">{{staffInfo.address || '未设置'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">紧急联系人</text>
          <text class="info-value">{{staffInfo.emergencyContact || '未设置'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">紧急联系电话</text>
          <text class="info-value">{{staffInfo.emergencyPhone || '未设置'}}</text>
        </view>
      </view>
    </view>

    <!-- 工作信息 -->
    <view class="info-section">
      <view class="section-title">
        <view class="section-icon-work"></view>
        <text>工作信息</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">员工编号</text>
          <text class="info-value">{{staffInfo.employeeId}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">部门</text>
          <text class="info-value">{{staffInfo.department}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">职位</text>
          <text class="info-value">{{staffInfo.position}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">入职日期</text>
          <text class="info-value">{{staffInfo.entryDate}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">状态</text>
          <text class="info-value {{staffInfo.status === '在职' ? 'active-text' : 'inactive-text'}}">{{staffInfo.status}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">薪资</text>
          <text class="info-value sensitive">¥ {{staffInfo.salary || '保密'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">绩效</text>
          <text class="info-value">{{staffInfo.performance || '未评定'}}</text>
        </view>
      </view>
    </view>

    <!-- 教育背景 -->
    <view class="info-section">
      <view class="section-title">
        <view class="section-icon-education"></view>
        <text>教育背景</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">学历</text>
          <text class="info-value">{{staffInfo.education || '未设置'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">专业</text>
          <text class="info-value">{{staffInfo.major || '未设置'}}</text>
        </view>
      </view>
    </view>

    <!-- 技能与证书 -->
    <view class="info-section">
      <view class="section-title">
        <view class="section-icon-skill"></view>
        <text>技能与证书</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">技能</text>
          <view class="tag-list" wx:if="{{staffInfo.skills && staffInfo.skills.length > 0}}">
            <text class="tag" wx:for="{{staffInfo.skills}}" wx:key="*this">{{item}}</text>
          </view>
          <text class="info-value" wx:else>未设置</text>
        </view>
        <view class="info-item">
          <text class="info-label">证书</text>
          <view class="tag-list" wx:if="{{staffInfo.certificates && staffInfo.certificates.length > 0}}">
            <text class="tag" wx:for="{{staffInfo.certificates}}" wx:key="*this">{{item}}</text>
          </view>
          <text class="info-value" wx:else>未设置</text>
        </view>
      </view>
    </view>

    <!-- 位置信息 -->
    <view class="info-section">
      <view class="section-title">
        <view class="section-icon-location"></view>
        <text>位置信息</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">当前位置</text>
          <text class="info-value">{{staffInfo.currentLocation || '未获取'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">最后更新</text>
          <text class="info-value">{{staffInfo.locationUpdateTime || '未更新'}}</text>
        </view>
        <view class="location-map" wx:if="{{staffInfo.locationLatitude && staffInfo.locationLongitude}}">
          <map
            longitude="{{staffInfo.locationLongitude}}"
            latitude="{{staffInfo.locationLatitude}}"
            scale="16"
            markers="{{[{longitude: staffInfo.locationLongitude, latitude: staffInfo.locationLatitude, iconPath: '/images/icons/location-marker.png', width: 30, height: 30}]}}"
            style="width: 100%; height: 300rpx;"
            bindtap="viewLargeMap"
          ></map>
        </view>
      </view>
    </view>

    <!-- 健康信息 -->
    <view class="info-section">
      <view class="section-title">
        <view class="section-icon-health"></view>
        <text>健康信息</text>
      </view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">心率</text>
          <text class="info-value {{staffInfo.heartRate > 100 || staffInfo.heartRate < 60 ? 'warning-text' : ''}}">{{staffInfo.heartRate || '--'}} 次/分</text>
        </view>
        <view class="info-item">
          <text class="info-label">血压</text>
          <text class="info-value">{{staffInfo.bloodPressure || '--'}}</text>
        </view>
        <view class="info-item">
          <text class="info-label">体温</text>
          <text class="info-value {{staffInfo.temperature > 37.3 ? 'warning-text' : ''}}">{{staffInfo.temperature || '--'}} ℃</text>
        </view>
        <view class="info-item">
          <text class="info-label">步数</text>
          <text class="info-value">{{staffInfo.steps || '--'}} 步</text>
        </view>
        <view class="info-item">
          <text class="info-label">最后更新</text>
          <text class="info-value">{{staffInfo.healthUpdateTime || '未更新'}}</text>
        </view>
      </view>
    </view>

    <!-- 备注 -->
    <view class="info-section">
      <view class="section-title">
        <view class="section-icon-note"></view>
        <text>备注</text>
      </view>
      <view class="info-list">
        <view class="info-note">
          {{staffInfo.notes || '暂无备注'}}
        </view>
      </view>
    </view>

    <!-- 证件照片 -->
    <view class="info-section" wx:if="{{staffInfo.idCardPhoto}}">
      <view class="section-title">
        <view class="section-icon-photo"></view>
        <text>证件照片</text>
      </view>
      <view class="photo-list">
        <view class="photo-item">
          <text class="photo-label">工作证件照</text>
          <image class="photo-image" src="{{staffInfo.idCardPhoto}}" mode="aspectFill" bindtap="viewImage" data-url="{{staffInfo.idCardPhoto}}"></image>
        </view>
      </view>
    </view>

    <!-- 底部空白 -->
    <view style="height: 40rpx;"></view>
  </block>

  <!-- 操作菜单 -->
  <view class="action-sheet {{showActionSheet ? 'show' : ''}}" bindtap="hideActions">
    <view class="action-sheet-mask"></view>
    <view class="action-sheet-content" catchtap="stopPropagation">
      <view class="action-sheet-title">员工操作</view>
      <view class="action-sheet-items">
        <view class="action-sheet-item" wx:for="{{actions}}" wx:key="name" bindtap="handleActionClick" data-index="{{index}}">
          <view class="action-sheet-icon {{item.icon}}" style="color: {{item.color}};"></view>
          <text style="color: {{item.color}};">{{item.name}}</text>
        </view>
      </view>
      <view class="action-sheet-cancel" bindtap="hideActions">取消</view>
    </view>
  </view>
</view>
