// app.js
// 引入页面生命周期函数，添加暗黑模式支持
require('./utils/page-lifecycle.js')

const REQUEST = require('./utils/request.js')
const commApi=require('./api/commApi.js')

// 引入访客管理和通知管理
const VisitorManager = require('./utils/visitor-manager')
const NotificationManager = require('./utils/notification-manager')
// 引入积分工具类
const PointsUtil = require('./utils/points')
// 引入工单管理工具类
const WorkOrderManager = require('./utils/workorder-manager')
// 引入底部导航栏管理工具
const TabbarManager = require('./utils/tabbar-manager')

const visitorsApi = require('./api/visitorsApi');
const dateUtil = require('./utils/dateUtil');
// 引入WebSocket管理器
const WebSocketManager = require('@/utils/websocket-manager');


App({
  onLaunch: function () {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())

    //接口地址

    wx.setStorageSync('apiUrl', 'http://10.37.13.5:8080')

 
    wx.setStorageSync('logs', logs)

    // 初始化应用登录状态
    this.initializeAppLogin()

    // 检查是否有即将到期的访客码
    // this.checkExpiringVisitors()

    // 初始化用户积分
    this.initUserPoints()

    // 初始化工单模拟数据
    WorkOrderManager.initMockData()

    // 检查工单提醒
    this.checkWorkOrderReminders()

    // 初始化底部导航栏管理器
    TabbarManager.init()

    // 初始化WebSocket管理器
    this.initWebSocket()

    // 加载暗黑模式设置
    const darkModeEnabled = wx.getStorageSync('darkModeEnabled') || false
    this.globalData.darkMode = darkModeEnabled

    // 如果开启了暗黑模式，则应用暗黑模式的导航栏颜色
    if (darkModeEnabled) {
      // 设置导航栏颜色
      wx.setNavigationBarColor({
        frontColor: '#ffffff',
        backgroundColor: '#1f1f1f'
      })

      // 设置标签栏样式
      try {
        wx.setTabBarStyle({
          color: '#8F8F8F',
          selectedColor: '#ffffff',
          backgroundColor: '#1f1f1f',
          borderStyle: 'black'
        })
      } catch (error) {
        console.log('设置标签栏样式失败，可能当前页面没有标签栏：', error)
      }

      // 设置状态栏样式
      wx.setBackgroundColor({
        backgroundColor: '#1c1c1e'
      })

      // 设置下拉背景色
      wx.setBackgroundTextStyle({
        textStyle: 'light'
      })
    }

    // 获取用户信息
    wx.getSetting({
      success: res => {
        if (res.authSetting['scope.userInfo']) {
          // 已经授权，可以直接调用 getUserInfo 获取头像昵称，不会弹框
          wx.getUserInfo({
            success: res => {
              // 可以将 res 发送给后台解码出 unionId
              this.globalData.userInfo = res.userInfo

              // 由于 getUserInfo 是网络请求，可能会在 Page.onLoad 之后才返回
              // 所以此处加入 callback 以防止这种情况
              if (this.userInfoReadyCallback) {
                this.userInfoReadyCallback(res)
              }
            }
          })
        }
      }
    })
  },

  // 初始化应用登录状态
  initializeAppLogin: function () {

    // 设置全局登录状态为初始化中
    this.globalData.isLoginInitializing = true;
    this.globalData.loginInitPromise = REQUEST.initializeApp()
      .then((result) => {
       
        this.globalData.isAuthenticated = true;
        this.globalData.isLoginInitializing = false;

        // 触发登录完成事件
        if (this.onLoginComplete) {
          this.onLoginComplete(result);
        }

        return result;
      })
      .catch((error) => {
        console.error('App: 登录初始化失败', error);
        this.globalData.isAuthenticated = false;
        this.globalData.isLoginInitializing = false;

        // 触发登录失败事件
        if (this.onLoginFailed) {
          this.onLoginFailed(error);
        }

        return Promise.reject(error);
      });

    return this.globalData.loginInitPromise;
  },

  // 等待登录完成
  waitForLogin: function () {
    if (this.globalData.isAuthenticated) {
      return Promise.resolve(true);
    }

    if (this.globalData.loginInitPromise) {
      return this.globalData.loginInitPromise;
    }

    // 如果没有正在进行的登录，重新初始化
    return this.initializeAppLogin();
  },

  // 检查是否有即将到期的访客码
  checkExpiringVisitors: function () {

    var params = {
      pageNum: 1,
      pageSize: 100
    }

    visitorsApi.getVisitorList(params).then(res => {
     

        // 处理分页数据或直接数组数据
        const visitors = Array.isArray(res.list) ? res.list :
          Array.isArray(res) ? res : [];

        visitors.forEach(visitor => {
          visitor.endTime = dateUtil.addHoursToTime(visitor.stayDuration)
        });

        // 如果没有访客，直接返回
        if (!visitors || !visitors.length) return;

        var currentTime = dateUtil.getCurrentTime()
        // 筛选出状态为待到访的访客
        // const visitors = visitors.filter(visitor => new Date( visitor.visitTime) < new Date(currentTime))
        //发送通知或其他

    })
      .catch(err => {

      });

    // 设置定时器，每小时检查一次
    setTimeout(() => {
      this.checkExpiringVisitors();
    }, 60 * 60 * 1000); // 1小时
  },

  // 初始化用户积分
  initUserPoints: function () {
    // 获取用户积分
    PointsUtil.getUserPoints().then(points => {
      // 更新全局数据
      this.globalData.userPoints = points;
    
    }).catch(err => {
      console.error('获取用户积分失败:', err);
    });
  },

  // 检查工单提醒
  checkWorkOrderReminders: function () {
    // 检查并发送工单提醒
    WorkOrderManager.checkAndSendReminders()
      .then(sentReminders => {
        if (sentReminders && sentReminders.length > 0) {

        }
      })
      .catch(err => {
        console.error('检查工单提醒失败:', err);
      });

    // 设置定时器，每10分钟检查一次
    setTimeout(() => {
      this.checkWorkOrderReminders();
    }, 10 * 60 * 1000); // 10分钟
  },

  // 初始化WebSocket
  initWebSocket: function () {
    console.log('App: 开始初始化WebSocket');

    // 保存WebSocket管理器实例到全局数据（WebSocketManager已经是单例实例）
    this.globalData.websocketManager = WebSocketManager;

    // 设置WebSocket事件监听器
    this.setupWebSocketListeners();

    // 检查登录状态并初始化WebSocket
    this.checkWebSocketInitConditions();
  },

  // 设置WebSocket事件监听器
  setupWebSocketListeners: function () {
    const wsManager = this.globalData.websocketManager;

    console.log('App: 设置WebSocket事件监听器', {
      wsManager: !!wsManager,
      wsManagerType: typeof wsManager,
      hasOnMethod: !!(wsManager && wsManager.on)
    });

    // 监听连接打开事件
    wsManager.on('open', (res) => {
       
      console.log('App: WebSocket连接已建立', res);
      this.globalData.websocketConnected = true;

      // 可以在这里发送一些初始化消息
      // 例如：注册用户在线状态等
    });

    // 注释掉通用消息监听，改为监听具体的事件类型
    // wsManager.on('message', (data) => {
    //   console.log('App: 收到WebSocket消息', data);
    //   // 根据消息类型进行处理
    //   this.handleWebSocketMessage(data);
    // });

    // 监听连接关闭事件
    wsManager.on('close', (res) => {
       
      console.log('App: WebSocket连接已关闭', res);
      this.globalData.websocketConnected = false;
    });

    // 监听连接错误事件
    wsManager.on('error', (res) => {
       
      console.error('App: WebSocket连接错误', res);
      this.globalData.websocketConnected = false;
    });

    // 监听重连事件
    wsManager.on('reconnect', (res) => {

      console.log('App: WebSocket重连事件', res);
      if (res.success) {
        this.globalData.websocketConnected = true;
        console.log('reconnect',res)

      } else if (res.reason === 'max_attempts_reached') {

        console.log('连接失败',res)


      }
    });

    // 监听系统消息事件
    wsManager.on('systemMessage', (message) => {
      console.log('App: 收到系统消息事件', message);
      this.handleSystemMessageReceived(message);
    });

    // 监听通知公告事件
    wsManager.on('noticeMessage', (message) => {
      console.log('App: 收到通知公告事件', message);
      this.handleNoticeMessageReceived(message);
    });

    // 监听私信事件
    wsManager.on('privateMessage', (message) => {
      console.log('App: 收到私信事件', message);
      this.handlePrivateMessageReceived(message);
    });
  },

  // 检查WebSocket初始化条件
  checkWebSocketInitConditions: function () {
    console.log('App: 初始化WebSocket管理器');

    // 直接初始化WebSocket管理器
    // 新的管理器会自动处理token状态检测和连接管理
    this.globalData.websocketManager.init();
  },

  // 处理WebSocket消息
  handleWebSocketMessage: function (data) {
    try {
      // 根据消息主题进行不同处理
      switch (data.topic) {
        case 'send_private_message':
          // 处理接收到的私聊消息
          this.handlePrivateMessageReceived(data.message);
          break;

        case 'send_private_message_response':
          // 处理私聊消息发送响应
          this.handlePrivateMessageResponse(data.message);
          break;

        case 'subs_sys_message':
          // 处理系统消息推送
          this.handleSystemMessageReceived(data.message);
          break;

        case 'subs_notice':
          // 处理通知公告推送
          this.handleNoticeMessageReceived(data.message);
          break;

        case 'system_notification':
          // 处理系统通知
          this.handleSystemNotification(data.message);
          break;

        default:
          console.log('App: 未知的WebSocket消息类型', data.topic);
      }
    } catch (error) {
      console.error('App: 处理WebSocket消息失败', error, data);
    }
  },

  // 处理接收到的私聊消息
  handlePrivateMessageReceived: function (message) {
    console.log('App: 收到新的私聊消息', message);

    // 通知所有相关页面
    const pages = getCurrentPages();
    const currentPage = pages.length > 0 ? pages[pages.length - 1] : null;

    // 通知当前页面
    if (currentPage && currentPage.onWebSocketMessage) {
      currentPage.onWebSocketMessage('privateMessage', message);
    }

    // 特别通知messages页面（仅当不是当前页面时）
    pages.forEach(page => {
      // 检查是否是messages页面，且不是当前页面
      if (page.route === 'pages/messages/messages' &&
          page !== currentPage &&
          page.onWebSocketMessage) {
        page.onWebSocketMessage('privateMessage', message);
      }
    });
  },

  // 处理私聊消息发送响应
  handlePrivateMessageResponse: function (responseId) {
    console.log('App: 收到私聊消息发送响应', responseId);

    const isSuccess = responseId && Number(responseId) > 0;

    // 通知当前页面
    if (getCurrentPages().length > 0) {
      const currentPage = getCurrentPages()[getCurrentPages().length - 1];
      if (currentPage.onWebSocketMessage) {
        currentPage.onWebSocketMessage('messageResponse', { success: isSuccess, responseId });
      }
    }
  },

  // 处理系统消息推送
  handleSystemMessageReceived: function (message) {
    console.log('App: 收到新的系统消息', message);

    // 通知所有相关页面
    const pages = getCurrentPages();
    const currentPage = pages.length > 0 ? pages[pages.length - 1] : null;

    // 通知当前页面
    if (currentPage && currentPage.onWebSocketMessage) {
      currentPage.onWebSocketMessage('systemMessage', message);
    }

    // 特别通知messages页面（仅当不是当前页面时）
    pages.forEach(page => {
      // 检查是否是messages页面，且不是当前页面
      if (page.route === 'pages/messages/messages' &&
          page !== currentPage &&
          page.onWebSocketMessage) {
        page.onWebSocketMessage('systemMessage', message);
      }
    });
  },

  // 处理通知公告推送
  handleNoticeMessageReceived: function (message) {
    console.log('App: 收到新的通知公告', message);

    // 通知所有相关页面
    const pages = getCurrentPages();
    const currentPage = pages.length > 0 ? pages[pages.length - 1] : null;

    // 通知当前页面
    if (currentPage && currentPage.onWebSocketMessage) {
      currentPage.onWebSocketMessage('noticeMessage', message);
    }

    // 特别通知messages页面（仅当不是当前页面时）
    pages.forEach(page => {
      // 检查是否是messages页面，且不是当前页面
      if (page.route === 'pages/messages/messages' &&
          page !== currentPage &&
          page.onWebSocketMessage) {
        page.onWebSocketMessage('noticeMessage', message);
      }
    });
  },

  // 处理系统通知
  handleSystemNotification: function (notification) {
    console.log('App: 收到系统通知', notification);

    // 显示系统通知
    wx.showModal({
      title: '系统通知',
      content: notification.content || '您有新的系统消息',
      showCancel: false,
      confirmText: '确定'
    });
  },

  globalData: {
    userInfo: null,
    isAuthenticated: false,
    isLoginInitializing: false, // 是否正在初始化登录
    loginInitPromise: null, // 登录初始化Promise
    selectedCommunity: null,
    darkMode: false,
    baseUrl: 'https://api.example.com', // 替换为实际的API地址
    userPoints: 0, // 用户积分
    pointsChangeEvent: null, // 积分变动事件
    userRole: null, // 用户角色：admin(管理员) 或 worker(普通工作人员)
    websocketManager: null, // WebSocket管理器实例
    websocketConnected: false // WebSocket连接状态
  },

  // 登录并获取用户角色
  login: function () {
    return new Promise((resolve, reject) => {
      // 模拟登录请求
      setTimeout(() => {
        // 模拟从服务器获取用户角色
        // 实际应用中，这里应该是一个真实的API请求
        const userRole = Math.random() > 0.5 ? 'admin' : 'worker';

        // 存储用户角色
        this.globalData.userRole = userRole;
        wx.setStorageSync('userRole', userRole);

       
        resolve(userRole);
      }, 500);
    });
  },


  /**
   * 用户登录请求封装(解决onlaunch和onload执行顺序问题)
  */
  userLogin: function () {

    wx.showLoading({
      title: '正在获取用户信息',
      mask: true
    });

    // 使用request.js中的doLogin函数
    return REQUEST.doLogin().then(res => {

      wx.hideLoading();
      return res;
    }).catch(error => {
      wx.hideLoading();

   
      return Promise.reject(error);
    });
  },

  /**
   * 获取WebSocket管理器实例
   */
  getWebSocketManager: function () {
    return this.globalData.websocketManager;
  },

  /**
   * 检查WebSocket连接状态
   */
  isWebSocketConnected: function () {
    return this.globalData.websocketConnected;
  },

  /**
   * 发送WebSocket消息的便捷方法
   */
  sendWebSocketMessage: function (topic, data) {
    if (this.globalData.websocketManager) {
      return this.globalData.websocketManager.sendMessage({ topic, data });
    } else {
      console.warn('App: WebSocket管理器未初始化');
      return false;
    }
  },

  /**
   * 发送私聊消息的便捷方法
   */
  sendPrivateMessage: function (type, content, receiverId) {
    if (this.globalData.websocketManager) {
      return this.globalData.websocketManager.sendPrivateMessage(type, content, receiverId);
    } else {
      console.warn('App: WebSocket管理器未初始化');
      return false;
    }
  },




})
