// messages.js
const announcementApi = require('@/utils/announcement-api');
const commApi = require('@/api/commApi.js');
const noticeApi = require('@/api/notice.js');
const messageApi = require('@/api/messageApi.js');
const TabbarManager = require('../../utils/tabbar-manager.js');
const util = require('@/utils/util.js')

Page({
  data: {
    // 主要tab类型：system_message, notice_announcement, private_message
    mainTabs: [
      { key: 'system_message', name: '系统消息' },
      { key: 'notice_announcement', name: '通知公告' },
      { key: 'private_message', name: '站内私信' }
    ],
    currentMainTab: 'system_message',



    // 分别设置不同类型的消息列表
    systemMessages: [],           // 系统消息列表
    noticeMessages: [],           // 通知公告列表
    privateMessages: [],          // 站内私信列表

    // 分页参数也分别设置
    systemPageNum: 1,
    noticePageNum: 1,
    privatePageNum: 1,
    pageSize: 10,

    // 分页信息也分别设置
    systemTotal: 0,
    noticeTotal: 0,
    privateTotal: 0,

    systemHasMore: true,
    noticeHasMore: true,
    privateHasMore: true,

    // 加载状态也分别设置
    systemLoading: false,
    noticeLoading: false,
    privateLoading: false,

    // 未读数量
    unreadCounts: {
      system_message: 0,
      notice_announcement: 0,
      private_message: 0,
      total: 0
    },
    showMessageModal: false,
    currentMessage: {},
    currentMessageIcon: 'icon-property',

    // 图片访问路径
    apiUrl: wx.getStorageSync('apiUrl') + '/common-api/v1/file/',

    // 私信刷新标记
    needRefreshPrivateMessages: false
  },

  onLoad: function () {
 
  },

  onShow: function () {
    // 每次显示页面时刷新数据
    this.refreshCurrentTab();

    // 加载未读数量统计
    this.loadUnreadCounts();

    // 设置底部 tabBar 选中状态
    TabbarManager.setTabbarSelected(this, 3);

    // 设置WebSocket消息监听
    this.setupWebSocketListeners();
  },

  onHide: function () {
    // 页面隐藏时移除WebSocket监听
    this.removeWebSocketListeners();
  },

  onUnload: function () {
    // 页面卸载时移除WebSocket监听
    this.removeWebSocketListeners();
  },

  // 设置WebSocket事件监听
  setupWebSocketListeners: function () {
    const app = getApp();
    this.websocketManager = app.globalData.websocketManager;

    if (!this.websocketManager) {
      console.log('Messages页面: WebSocket管理器未找到');
      return;
    }

    // 绑定监听器到当前页面实例
    this.onPrivateMessage = this.handlePrivateMessage.bind(this);

    // 监听私聊消息
    this.websocketManager.on('privateMessage', this.onPrivateMessage);

    console.log('Messages页面: WebSocket监听器已设置');
  },

  // 移除WebSocket事件监听
  removeWebSocketListeners: function () {
    if (this.websocketManager && this.onPrivateMessage) {
      this.websocketManager.off('privateMessage', this.onPrivateMessage);
      console.log('Messages页面: WebSocket监听器已移除');
    }
  },

  // 处理接收到的私聊消息
  handlePrivateMessage: function (message) {
    console.log('Messages页面收到私聊消息:', message);

    // 更新未读数量
    const unreadCounts = { ...this.data.unreadCounts };
    unreadCounts.private_message += 1;
    unreadCounts.total += 1;
    this.setData({ unreadCounts });

    // 更新底部tabBar的未读数量
    TabbarManager.updateUnreadCount(unreadCounts.total);

    // 只有在私信tab时才处理消息列表更新
    if (this.data.currentMainTab === 'private_message') {
      // 检查是否已存在该用户的对话
      const existingMessages = this.data.privateMessages; // 修复：使用正确的数据字段
      const existingIndex = existingMessages.findIndex(item => item.userId === message.senderId);

      if (existingIndex >= 0) {
        // 更新现有对话的最新消息和时间
        const updatedMessages = [...existingMessages];
        updatedMessages[existingIndex] = {
          ...updatedMessages[existingIndex],
          content: message.content || '',
          time: this.formatMessageTime(message.createTime || new Date().toISOString()),
          lastMessageTime: message.createTime || new Date().toISOString(),
          type: message.type || 'text',
          countUnread: (updatedMessages[existingIndex].countUnread || 0) + 1 // 增加未读数量
        };

        // 将更新的对话移到列表顶部
        const updatedItem = updatedMessages.splice(existingIndex, 1)[0];
        updatedMessages.unshift(updatedItem);

        this.setData({
          privateMessages: updatedMessages // 修复：使用正确的数据字段
        });

        console.log('Messages页面: 已更新现有对话');
      } else {
        // 新的对话，刷新私信列表以获取完整信息
        console.log('Messages页面: 新对话，刷新私信列表');
        this.loadPrivateMessages();
      }
    } else {
      // 不在私信tab时，标记需要刷新
      this.needRefreshPrivateMessages = true;
      console.log('Messages页面: 不在私信tab，标记需要刷新');
    }
  },

  // 处理WebSocket消息（由app.js调用）
  onWebSocketMessage: function (topic, data) {
    console.log('Messages页面收到WebSocket消息', { topic, data });

    switch (topic) {
      case 'privateMessage':
        this.handlePrivateMessage(data);
        break;
      case 'systemMessage':
        this.handleSystemMessage(data);
        break;
      case 'noticeMessage':
        this.handleNoticeMessage(data);
        break;
      default:
        console.log('Messages页面: 未知的WebSocket消息类型', topic);
    }
  },

  // 处理接收到的系统消息
  handleSystemMessage: function (message) {
    console.log('Messages页面收到系统消息:', message);

    // 更新未读数量
    const unreadCounts = { ...this.data.unreadCounts };
    unreadCounts.system_message += 1;
    unreadCounts.total += 1;
    this.setData({ unreadCounts });

    // 更新底部tabBar的未读数量
    TabbarManager.updateUnreadCount(unreadCounts.total);

    // 只有在系统消息tab时才直接添加到列表
    if (this.data.currentMainTab === 'system_message') {
      console.log('Messages页面: 收到系统消息，直接添加到列表');

      // 格式化消息数据，与列表item结构一致
      const formattedMessage = {
        id: message.id,
        title: message.title || '无标题',
        content: message.content || '',
        time: this.formatMessageTime(message.createTime),
        status: message.status || 'UNREAD', // 使用status字段，READ表示已读
        type: 'system',
        imageUrl: message.imageUrl,
        userId: message.userId
      };

      // 将新消息添加到列表顶部
      const systemMessages = [formattedMessage, ...this.data.systemMessages];

      this.setData({
        systemMessages: systemMessages,
        systemTotal: systemMessages.length
      });

      console.log('Messages页面: 已添加系统消息到列表顶部');
    } else {
      // 不在系统消息tab时，标记需要刷新
      this.needRefreshSystemMessages = true;
      console.log('Messages页面: 不在系统消息tab，标记需要刷新');
    }

    // 刷新未读数量
    this.loadUnreadCounts();
  },

  // 处理接收到的通知公告
  handleNoticeMessage: function (message) {
    console.log('Messages页面收到通知公告:', message);

    // 更新未读数量
    const unreadCounts = { ...this.data.unreadCounts };
    unreadCounts.notice_announcement += 1;
    unreadCounts.total += 1;
    this.setData({ unreadCounts });

    // 更新底部tabBar的未读数量
    TabbarManager.updateUnreadCount(unreadCounts.total);

    // 只有在通知公告tab时才直接添加到列表
    if (this.data.currentMainTab === 'notice_announcement') {
      console.log('Messages页面: 收到通知公告，直接添加到列表');

      // 格式化消息数据，与列表item结构一致
      const formattedMessage = {
        id: message.id,
        title: message.title || '无标题',
        content: message.content || '',
        time: this.formatMessageTime(message.createTime),
        read: message.read || false, // 通知公告使用read字段(boolean)
        type: message.type || 'property_notice',
        imageUrl: message.imageUrl,
        userId: message.userId
      };

      // 将新消息添加到列表顶部
      const noticeMessages = [formattedMessage, ...this.data.noticeMessages];

      this.setData({
        noticeMessages: noticeMessages,
        noticeTotal: noticeMessages.length
      });

      console.log('Messages页面: 已添加通知公告到列表顶部');
    } else {
      // 不在通知公告tab时，标记需要刷新
      this.needRefreshNoticeMessages = true;
      console.log('Messages页面: 不在通知公告tab，标记需要刷新');
    }

    // 刷新未读数量
    this.loadUnreadCounts();
  },

  // 下拉刷新

  onPullDownRefresh: async function () {
    try {
    debugger
      await this.refreshCurrentTab();
    } catch (e) {
      debugger
      wx.stopPullDownRefresh();
      console.error('刷新失败:', e);
    } finally {
      debugger
      wx.stopPullDownRefresh();
    }
  },

  // 上拉加载更多
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.messagesLoading) {
      this.loadMoreData();
    }
  },

  // 主tab切换
  switchMainTab: function (e) {
    const tab = e.currentTarget.dataset.tab;
    if (tab === this.data.currentMainTab) return;

    // 重置对应tab的分页参数
    const resetData = { currentMainTab: tab };

    switch (tab) {
      case 'system_message':
        resetData.systemPageNum = 1;
        resetData.systemMessages = [];
        resetData.systemHasMore = true;
        break;
      case 'notice_announcement':
        resetData.noticePageNum = 1;
        resetData.noticeMessages = [];
        resetData.noticeHasMore = true;
        break;
      case 'private_message':
        resetData.privatePageNum = 1;
        resetData.privateMessages = [];
        resetData.privateHasMore = true;
        break;
    }

    this.setData(resetData);

    // 如果切换到对应tab且有待刷新标记，则强制刷新
    if (tab === 'private_message' && this.needRefreshPrivateMessages) {
      this.needRefreshPrivateMessages = false;
      console.log('Messages页面: 切换到私信tab，执行待刷新操作');
    } else if (tab === 'system_message' && this.needRefreshSystemMessages) {
      this.needRefreshSystemMessages = false;
      console.log('Messages页面: 切换到系统消息tab，执行待刷新操作');
    } else if (tab === 'notice_announcement' && this.needRefreshNoticeMessages) {
      this.needRefreshNoticeMessages = false;
      console.log('Messages页面: 切换到通知公告tab，执行待刷新操作');
    }

    this.loadCurrentTabData();

    // 重新加载未读数量统计
    this.loadUnreadCounts();
  },



  // 加载未读数量统计
  loadUnreadCounts: function () {
     
    if (!util.checkAuthentication()) {
      console.log('用户未认证，无法获取未读数量');
      return;
    }
    var params={
      communityId: wx.getStorageSync('selectedCommunity').id
    }
    messageApi.getUnReadCount(params).then(res => {
      console.log('未读数量统计：', res);

      if (res ) {
        const unreadCounts = {
          system_message: res.sysMessageCount || 0,
          notice_announcement: res.noticeMessageCount || 0,
          private_message: res.privateMessageCount || 0,
          total: res.totalMessageCount || 0
        };
          
        this.setData({ unreadCounts });

        // 更新底部tabBar的未读数量
        TabbarManager.updateUnreadCount(unreadCounts.total);
      }
    }).catch(err => {
      console.error('获取未读数量失败：', err);
    });
  },

  // 加载当前tab的数据
  loadCurrentTabData: function () {
    const { currentMainTab } = this.data;
 
    switch (currentMainTab) {

      case 'system_message':
        this.loadSystemMessages();
        break;
      case 'notice_announcement':
        this.loadNoticeAnnouncementData();
        break;
      case 'private_message':
        this.loadPrivateMessages();
        break;
    }
  },

  // 刷新当前tab
  refreshCurrentTab: function () {
    const { currentMainTab } = this.data;
    const resetData = {};

    switch (currentMainTab) {
      case 'system_message':
        resetData.systemPageNum = 1;
        resetData.systemMessages = [];
        resetData.systemHasMore = true;
        break;
      case 'notice_announcement':
        resetData.noticePageNum = 1;
        resetData.noticeMessages = [];
        resetData.noticeHasMore = true;
        break;
      case 'private_message':
        resetData.privatePageNum = 1;
        resetData.privateMessages = [];
        resetData.privateHasMore = true;
        break;
    }

    this.setData(resetData);
    this.loadCurrentTabData();
  },

  // 加载更多数据
  loadMoreData: function () {
    const { currentMainTab } = this.data;

    // 检查对应tab的加载状态和是否还有更多数据
    let hasMore, isLoading;
    switch (currentMainTab) {
      case 'system_message':
        hasMore = this.data.systemHasMore;
        isLoading = this.data.systemLoading;
        break;
      case 'notice_announcement':
        hasMore = this.data.noticeHasMore;
        isLoading = this.data.noticeLoading;
        break;
      case 'private_message':
        hasMore = this.data.privateHasMore;
        isLoading = this.data.privateLoading;
        break;
      default:
        return;
    }

    if (!hasMore || isLoading) return;

    // 增加对应tab的页码
    const updateData = {};
    switch (currentMainTab) {
      case 'system_message':
        updateData.systemPageNum = this.data.systemPageNum + 1;
        break;
      case 'notice_announcement':
        updateData.noticePageNum = this.data.noticePageNum + 1;
        break;
      case 'private_message':
        updateData.privatePageNum = this.data.privatePageNum + 1;
        break;
    }

    this.setData(updateData);

    // 调用对应的加载方法
    switch (currentMainTab) {
      case 'system_message':
        this.loadSystemMessages(true);
        break;
      case 'notice_announcement':
        this.loadNoticeAnnouncementData(true);
        break;
      case 'private_message':
        this.loadPrivateMessages(true);
        break;
    }
  },



  // 加载系统消息
  loadSystemMessages: function (isLoadMore = false) {

    if (!util.checkAuthentication()) {
      console.log('用户未认证');
      return;
    }

    this.setData({ systemLoading: true });

    const params = {
      pageNum: this.data.systemPageNum,
      pageSize: this.data.pageSize
    };

    messageApi.getSystemMessageList(params).then(res => {

      console.log('系统消息数据：', res);

      if (res && res.list) {
        const messageList = res.list.map(item => ({
          id: item.id,
          title: item.title || '无标题',
          content: item.content || '',
          time: this.formatMessageTime(item.createTime),
          status: item.status || 'UNREAD', // 使用status字段，READ表示已读
          type: 'system',
          imageUrl: item.imageUrl,
          userId: item.userId
        }));

        // 处理分页数据
        const systemMessages = isLoadMore ? [...this.data.systemMessages, ...messageList] : messageList;
        const systemHasMore = res.total > this.data.systemPageNum * this.data.pageSize;

        this.setData({
          systemMessages: systemMessages,
          systemTotal: res.total || 0,
          systemHasMore: systemHasMore,
          systemLoading: false
        });
      } else {
        this.setData({ systemLoading: false });
      }
    }).catch(err => {
      console.error('获取系统消息失败：', err);
      this.setData({ systemLoading: false });
    });
  },

  // 加载通知公告数据
  loadNoticeAnnouncementData: function (isLoadMore = false) {
    if (!util.checkAuthentication()) {
      console.log('用户未认证');
      return;
    }

    const selectedCommunity = wx.getStorageSync('selectedCommunity');
    if (!selectedCommunity || !selectedCommunity.id) {
      console.error('未选择社区');
      return;
    }

    this.setData({ noticeLoading: true });

    // 获取通知类型字典和通知公告数据
    Promise.all([
      commApi.getDictByNameEn('notice_type'),
      noticeApi.getNoticePage({
        pageNum: this.data.noticePageNum,
        pageSize: this.data.pageSize,
        // 不传type参数，获取所有通知公告
        communityId: selectedCommunity.id
      })
    ]).then(([noticeTypesRes, noticeRes]) => {
      console.log('通知类型数据：', noticeTypesRes);
      console.log('通知公告数据：', noticeRes);

      // 构建通知类型映射
      const noticeTypeMap = {};
      if (noticeTypesRes && noticeTypesRes.length > 0) {
        noticeTypesRes.forEach(item => {
          noticeTypeMap[item.nameEn] = item.nameCn;
        });
      }

      if (noticeRes && noticeRes.list) {
        const messageList = noticeRes.list.map(item => {
          // 获取通知类型中文名
          const typeName = noticeTypeMap[item.type] || item.type || '通知';

          return {
            id: item.id,
            title: `[${typeName}] ${item.title || '无标题'}`, // 在标题前添加通知类型
            content: item.content || '',
            time: this.formatMessageTime(item.createTime),
            read: item.read !== undefined ? item.read : false,
            type: item.type,
            originalTitle: item.title || '无标题', // 保留原始标题
            createBy: item.createBy,
            imageUrl: item.imageUrl,
            sort: item.sort,
            targetIds: item.targetIds,
            targetType: item.targetType,
            top: item.top
          };
        });

        // 处理分页数据
        const noticeMessages = isLoadMore ? [...this.data.noticeMessages, ...messageList] : messageList;
        const noticeHasMore = noticeRes.total > this.data.noticePageNum * this.data.pageSize;

        this.setData({
          noticeMessages: noticeMessages,
          noticeTotal: noticeRes.total || 0,
          noticeHasMore: noticeHasMore,
          noticeLoading: false
        });
      } else {
        this.setData({ noticeLoading: false });
      }
    }).catch(err => {
      console.error('获取通知公告失败：', err);
      this.setData({ noticeLoading: false });
    });
  },

  // 加载站内私信
  loadPrivateMessages: function (isLoadMore = false) {
    if (!util.checkAuthentication()) {
      console.log('用户未认证');
      return;
    }

    this.setData({ privateLoading: true });

    messageApi.getPrivateMessageList().then(res => {
      console.log('站内私信数据：', res);

      if (res ) {
        const messageList = res.map(item => ({
          id: item.userId, // 使用userId作为唯一标识
          userId: item.userId,
          userName: item.userName || '未知用户',
          avatarUrl: item.avatarUrl,
          title: item.userName || '未知用户', // 私信列表显示用户名作为标题
          content: item.content || '',
          time: this.formatMessageTime(item.createTime),
          countUnread: item.countUnread || 0, // 保留原始未读数量字段
          type: item.type || 'text',
          lastMessageTime: item.createTime
        }));

        // 私信列表不需要分页，直接显示全部
        this.setData({
          privateMessages: messageList,
          privateTotal: messageList.length,
          privateHasMore: false,
          privateLoading: false
        });
      } else {
        this.setData({ privateLoading: false });
      }
    }).catch(err => {
      console.error('获取站内私信失败：', err);
      this.setData({ privateLoading: false });
    });
  },

  // 处理私信列表项点击（进入聊天页面）
  handlePrivateMessageTap: function (e) {
    const message = e.currentTarget.dataset.message;
 
    // 跳转到聊天页面
    wx.navigateTo({
      url: `/servicePackage/pages/messages/chat?targetId=${message.userId}&targetName=${message.userName}&targetAvatar=${message.avatarUrl || ''}`
    });
  },

  // 格式化消息时间
  formatMessageTime: function (timeString) {
    if (!timeString) return '刚刚';

    try {
      const messageTime = new Date(timeString);
      const now = new Date();
      const diffDays = Math.floor((now - messageTime) / (24 * 60 * 60 * 1000));

      if (diffDays === 0) {
        return '今天 ' + messageTime.getHours().toString().padStart(2, '0') + ':' +
          messageTime.getMinutes().toString().padStart(2, '0');
      } else if (diffDays === 1) {
        return '昨天 ' + messageTime.getHours().toString().padStart(2, '0') + ':' +
          messageTime.getMinutes().toString().padStart(2, '0');
      } else {
        return (messageTime.getMonth() + 1).toString().padStart(2, '0') + '-' +
          messageTime.getDate().toString().padStart(2, '0') + ' ' +
          messageTime.getHours().toString().padStart(2, '0') + ':' +
          messageTime.getMinutes().toString().padStart(2, '0');
      }
    } catch (error) {
      console.error('时间格式化失败：', error);
      return '刚刚';
    }
  },

  // 标签滚动提示点击
  onScrollHintTap: function () {
    // 触发标签容器滚动
    const query = wx.createSelectorQuery();
    query.select('.message-tabs-container').scrollTo({
      left: 200, // 向右滚动200rpx
      animated: true
    });
    query.exec();
  },

  // 加载公告数据（保留原有方法，用于兼容）
  loadAnnouncementData: function () {
    wx.showLoading({
      title: '加载中...'
    });

    // 调用API获取公告列表
    announcementApi.getAnnouncementList()
      .then(res => {
        const announcements = res.list || [];

        // 按类型分类公告
        const propertyNotices = [];
        const activityNotices = [];
        const emergencyNotices = [];

        // 处理公告数据
        announcements.forEach(item => {
          if (item.status !== 'published') return;

          // 格式化时间
          let timeText = '';
          if (typeof item.publishTime === 'string') {
            const publishTime = new Date(item.publishTime);
            const now = new Date();
            const diffDays = Math.floor((now - publishTime) / (24 * 60 * 60 * 1000));

            if (diffDays === 0) {
              timeText = '今天 ' + publishTime.getHours().toString().padStart(2, '0') + ':' +
                publishTime.getMinutes().toString().padStart(2, '0');
            } else if (diffDays === 1) {
              timeText = '昨天 ' + publishTime.getHours().toString().padStart(2, '0') + ':' +
                publishTime.getMinutes().toString().padStart(2, '0');
            } else {
              timeText = (publishTime.getMonth() + 1).toString().padStart(2, '0') + '-' +
                publishTime.getDate().toString().padStart(2, '0') + ' ' +
                publishTime.getHours().toString().padStart(2, '0') + ':' +
                publishTime.getMinutes().toString().padStart(2, '0');
            }
          } else {
            timeText = '刚刚';
          }

          // 创建通知对象
          const notice = {
            id: item.id,
            title: item.title,
            time: timeText,
            read: false, // 默认未读
            content: item.content
          };

          // 根据类型分类
          switch (item.type) {
            case 'property_notice':
              propertyNotices.push(notice);
              break;
            case 'activity_notice':
              activityNotices.push(notice);
              break;
            case 'emergency_notice':
              emergencyNotices.push(notice);
              break;
          }
        });

        // 更新数据
        this.setData({
          'messages.property': propertyNotices,
          'messages.community': activityNotices,
          'messages.emergency': emergencyNotices,
          'unreadCounts.property': propertyNotices.filter(item => !item.read).length,
          'unreadCounts.community': activityNotices.filter(item => !item.read).length,
          'unreadCounts.emergency': emergencyNotices.filter(item => !item.read).length
        });

        wx.hideLoading();
      })
      .catch(err => {
        console.error('加载公告数据失败', err);
        wx.hideLoading();

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },



  showMessageDetail: function (e) {
    const message = e.currentTarget.dataset.message;
    const messageType = e.currentTarget.dataset.type || this.data.currentMainTab;

    // 如果是站内私信，跳转到聊天页面
    if (messageType === 'private_message') {
      this.handlePrivateMessageTap(e);
      return;
    }

    // 标记为已读
    let needMarkRead = false;
    if (messageType === 'system_message' && message.status !== 'read') {
      needMarkRead = true;
    } else if (messageType === 'notice_announcement' && !message.read) {
      needMarkRead = true;
    }

    if (needMarkRead) {
      this.markMessageAsRead(message, messageType);
    }

    // 根据消息类型设置图标
    let icon = 'icon-property';
    if (messageType === 'system_message') {
      icon = 'icon-system';
    } else if (message.type === 'emergency_notice') {
      icon = 'icon-emergency';
    } else if (message.type === 'community_notice') {
      icon = 'icon-community';
    } else if (message.type === 'property_notice') {
      icon = 'icon-property';
    } else if (message.type === 'customer_message') {
      icon = 'icon-service';
    }

    // 生成消息内容
    let content = this.generateMessageContent(message);

    this.setData({
      showMessageModal: true,
      currentMessage: {
        ...message,
        content
      },
      currentMessageIcon: icon
    });
  },

  closeMessageModal: function () {
    this.setData({
      showMessageModal: false
    });
  },

  // 隐藏消息弹窗
  hideMessageModal: function () {
    this.setData({
      showMessageModal: false
    });
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 空方法，用于阻止事件冒泡
  },

  stopPropagation: function (e) {
    // 阻止事件冒泡
  },

  markMessageAsRead: function (message, messageType) {
    // 根据消息类型获取对应的消息列表
    let messages, listKey;
    switch (messageType) {
      case 'system_message':
        messages = [...this.data.systemMessages];
        listKey = 'systemMessages';
        break;
      case 'notice_announcement':
        messages = [...this.data.noticeMessages];
        listKey = 'noticeMessages';
        break;
      case 'private_message':
        messages = [...this.data.privateMessages];
        listKey = 'privateMessages';
        break;
      default:
        console.warn('未知的消息类型:', messageType);
        return;
    }

    const unreadCounts = { ...this.data.unreadCounts };

    for (let i = 0; i < messages.length; i++) {
      if (messages[i].id === message.id) {
        // 根据消息类型设置已读状态
        if (messageType === 'system_message') {
          messages[i].status = 'read';
        } else {
          messages[i].read = true;
        }

        // 根据消息类型调用不同的API
        let apiPromise;
        switch (messageType) {
          case 'system_message':
            apiPromise = messageApi.markSystemMessageRead(messages[i].id);
            unreadCounts.system_message = Math.max(0, unreadCounts.system_message - 1);
            break;
          case 'notice_announcement':
            apiPromise = noticeApi.setNoticeRead(messages[i].id);
            unreadCounts.notice_announcement = Math.max(0, unreadCounts.notice_announcement - 1);
            break;
          case 'private_message':
            apiPromise = messageApi.markPrivateMessageRead(messages[i].id);
            unreadCounts.private_message = Math.max(0, unreadCounts.private_message - 1);
            break;
        }

        // 调用API设置已读状态
        apiPromise.then(res => {
          console.log(`${messageType} 设置已读成功`);

          // 更新总未读数量
          unreadCounts.total = Math.max(0, unreadCounts.total - 1);
          this.setData({ unreadCounts });

          // 更新底部tabBar的未读数量
          TabbarManager.updateUnreadCount(unreadCounts.total);
        }).catch(err => {
          console.error(`${messageType} 设置已读失败`, err);
          // 如果API调用失败，恢复消息的未读状态
          if (messageType === 'system_message') {
            messages[i].status = 'UNREAD';
          } else {
            messages[i].read = false;
          }
          const updateData = {};
          updateData[listKey] = messages;
          this.setData(updateData);
        });

        break;
      }
    }

    // 立即更新本地UI状态
    const updateData = {};
    updateData[listKey] = messages;
    this.setData(updateData);
  },

  generateMessageContent: function (message) {
    // 如果消息有内容，直接使用
    if (message.content) {
      // 如果内容是HTML格式，去除HTML标签
      if (message.content.includes('<')) {
        return message.content.replace(/<[^>]+>/g, '');
      }
      return message.content;
    }


  },





})
