// pages/property/staff/staff-edit.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    mode: 'add', // 'add' 或 'edit'
    staffId: '',
    isLoading: true,
    isSubmitting: false,

    // 表单数据
    name: '',
    gender: '',
    genderIndex: -1,
    age: '',
    phone: '',
    idCard: '',
    employeeId: '',
    organization: '',
    organizationIndex: -1,
    orgId: '', // 组织ID
    position: '',
    positionIndex: -1,
    positionId: '', // 职位ID
    entryDate: '',
    status: '',
    statusIndex: -1,
    email: '',
    address: '',
    major: '',
    skills: '',
    certificates: '',
    performance: '',
    salary: '',
    notes: '',
    idCardPhotoPath: '', // 证件照路径

    // 表单验证状态
    nameValid: false,
    nameError: false,
    phoneValid: false,
    phoneError: false,
    idCardValid: false,
    idCardError: false,
    employeeIdValid: false,
    employeeIdError: false,
    organizationValid: false,
    organizationError: false,
    positionValid: false,
    positionError: false,

    // 选项数据（从字典和API获取）
    genders: [],
    genderOptions: [],
    organizations: [],
    orgList: [],
    positions: [],
    positionList: [],
    statuses: [],
    statusOptions: [],

    // 原始数据（编辑模式使用）
    rawData: null,

    errorMsg: ''
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 设置模式和员工ID
    if (options.mode) {
      this.setData({
        mode: options.mode
      });
    }

    if (options.id && options.mode === 'edit') {
      this.setData({
        staffId: options.id
      });

      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '编辑员工'
      });

      // 加载员工数据
      this.loadStaffData();
    } else {
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '添加员工'
      });

      // 初始化基础数据
      this.initializeData();
    }
  },

  // 初始化基础数据
  initializeData: function() {
    // 并行加载基础数据
    Promise.all([
      this.loadDictData(),
      this.loadOrgData(),
      this.loadPositionData()
    ]).then(() => {
      // 设置默认值
      this.setData({
        entryDate: this.formatDate(new Date()),
        isLoading: false
      });
      console.log('基础数据加载完成');
    }).catch(err => {
      console.error('基础数据加载失败:', err);
      this.setData({
        entryDate: this.formatDate(new Date()),
        isLoading: false
      });
    });
  },

  // 加载字典数据
  loadDictData: function() {
    return new Promise((resolve) => {
      const personStatusDict = util.getDictByNameEn('person_status');
      const genderDict = util.getDictByNameEn('gender');

      const statusOptions = personStatusDict && personStatusDict.length > 0 ? personStatusDict[0].children : [];
      const genderOptions = genderDict && genderDict.length > 0 ? genderDict[0].children : [];

      // 转换为选择器格式
      const statuses = statusOptions.map(item => item.nameCn);
      const genders = genderOptions.map(item => item.nameCn);

      this.setData({
        statusOptions: statusOptions,
        statuses: statuses,
        genderOptions: genderOptions,
        genders: genders
      });

      console.log('字典数据加载完成:', { statuses, genders });
      resolve();
    });
  },

  // 加载组织数据
  loadOrgData: function() {
    return new Promise((resolve) => {
      const propertyApi = require('@/api/propertyApi.js');
      propertyApi.getOrgTree().then(res => {
        if (res && res.list && Array.isArray(res.list)) {
          const orgList = res.list;

          // 提取所有组织名称用于选择器
          const organizations = [];
          const extractOrgs = (orgs) => {
            orgs.forEach(org => {
              organizations.push(org.orgName);
              if (org.children && org.children.length > 0) {
                extractOrgs(org.children);
              }
            });
          };

          extractOrgs(orgList);

          this.setData({
            orgList: orgList,
            organizations: organizations
          });

          console.log('组织数据加载完成:', organizations);
        }
        resolve();
      }).catch(err => {
        console.error('获取组织数据失败:', err);
        resolve();
      });
    });
  },

  // 加载职位数据
  loadPositionData: function() {
    return new Promise((resolve) => {
      const commApi = require('@/api/commApi.js');
      const params = { pageNum: 1, pageSize: 100 };

      commApi.getPositionPage(params).then(res => {
        if (res && res.list && Array.isArray(res.list)) {
          const positionList = res.list;
          const positions = positionList.map(item => item.positionName);

          this.setData({
            positionList: positionList,
            positions: positions
          });

          console.log('职位数据加载完成:', positions);
        }
        resolve();
      }).catch(err => {
        console.error('获取职位数据失败:', err);
        resolve();
      });
    });
  },

  // 加载员工数据
  loadStaffData: function() {
    this.setData({
      isLoading: true
    });

    // 先初始化基础数据
    this.initializeData().then(() => {
      // 从存储中获取员工数据
      const rawData = wx.getStorageSync('editStaffData');

      if (rawData) {
        console.log('获取到员工原始数据:', rawData);
        this.fillFormData(rawData);
      } else {
        console.error('未找到员工数据');
        wx.showToast({
          title: '员工数据不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    });
  },

  // 填充表单数据
  fillFormData: function(rawData) {
    console.log('填充表单数据:', rawData);

    // 获取组织名称和索引
    const orgName = this.getOrgNameById(rawData.orgId);
    const organizationIndex = this.data.organizations.findIndex(item => item === orgName);

    // 获取职位名称和索引
    const positionName = this.getPositionNameById(rawData.positionId);
    const positionIndex = this.data.positions.findIndex(item => item === positionName);

    // 获取状态显示文本和索引
    const statusText = this.getStatusTextByCode(rawData.status);
    const statusIndex = this.data.statuses.findIndex(item => item === statusText);

    // 获取性别显示文本和索引
    const genderText = this.getGenderTextByCode(rawData.gender);
    const genderIndex = this.data.genders.findIndex(item => item === genderText);

    // 处理证件照路径
    const idCardPhotoPath = rawData.media ?
      `${wx.getStorageSync('apiUrl')}/common-api/v1/file/${rawData.media}` :
      '';

    this.setData({
      rawData: rawData, // 保存原始数据
      name: rawData.personName || '',
      gender: genderText,
      genderIndex: genderIndex !== -1 ? genderIndex : -1,
      age: rawData.age ? rawData.age.toString() : '',
      phone: rawData.phone || '',
      idCard: rawData.idCard || '',
      employeeId: rawData.personNumber || '',
      organization: orgName,
      organizationIndex: organizationIndex !== -1 ? organizationIndex : -1,
      orgId: rawData.orgId || '',
      position: positionName,
      positionIndex: positionIndex !== -1 ? positionIndex : -1,
      positionId: rawData.positionId || '',
      entryDate: rawData.entryTime ? rawData.entryTime.split(' ')[0] : '',
      status: statusText,
      statusIndex: statusIndex !== -1 ? statusIndex : -1,
      email: rawData.email || '',
      address: rawData.address || '',
      major: rawData.major || '',
      skills: rawData.skills || '',
      certificates: rawData.certificates || '',
      performance: rawData.performance || '',
      salary: rawData.salary ? rawData.salary.toString() : '',
      notes: rawData.note || '',
      idCardPhotoPath: idCardPhotoPath,

      // 设置验证状态
      nameValid: !!rawData.personName,
      phoneValid: !!rawData.phone,
      idCardValid: !!rawData.idCard,
      employeeIdValid: !!rawData.personNumber,
      organizationValid: !!orgName,
      positionValid: !!positionName,

      isLoading: false
    });

    console.log('表单数据填充完成');
  },

  // 根据组织ID获取组织名称
  getOrgNameById: function(orgId) {
    if (!orgId || !this.data.orgList || !this.data.orgList.length) return '';

    const findOrg = (orgs) => {
      for (let org of orgs) {
        if (org.id === orgId) {
          return org.orgName;
        }
        if (org.children && org.children.length > 0) {
          const found = findOrg(org.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findOrg(this.data.orgList) || '';
  },

  // 根据职位ID获取职位名称
  getPositionNameById: function(positionId) {
    if (!positionId || !this.data.positionList || !this.data.positionList.length) return '';

    const position = this.data.positionList.find(p => p.id === positionId);
    return position ? position.positionName : '';
  },

  // 根据状态码获取状态文本
  getStatusTextByCode: function(statusCode) {
    if (!statusCode || !this.data.statusOptions || !this.data.statusOptions.length) return '';

    const status = this.data.statusOptions.find(s => s.nameEn === statusCode);
    return status ? status.nameCn : '';
  },

  // 根据性别码获取性别文本
  getGenderTextByCode: function(genderCode) {
    if (!genderCode || !this.data.genderOptions || !this.data.genderOptions.length) return '';

    const gender = this.data.genderOptions.find(g => g.nameEn === genderCode);
    return gender ? gender.nameCn : '';
  },

  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 输入姓名
  inputName: function(e) {
    this.setData({
      name: e.detail.value
    });
    this.validateName();
  },

  // 验证姓名
  validateName: function() {
    const name = this.data.name.trim();
    const isValid = name.length >= 2;
    this.setData({
      nameValid: isValid,
      nameError: name.length > 0 && !isValid
    });
    return isValid;
  },

  // 选择性别
  bindGenderChange: function(e) {
    this.setData({
      gender: this.data.genders[e.detail.value]
    });
  },

  // 输入年龄
  inputAge: function(e) {
    this.setData({
      age: e.detail.value
    });
  },

  // 输入手机号
  inputPhone: function(e) {
    this.setData({
      phone: e.detail.value
    });
    this.validatePhone();
  },

  // 验证手机号
  validatePhone: function() {
    const util = require('@/utils/util');
    const phone = this.data.phone.trim();
    const isValid = util.validatePhone(phone);
    this.setData({
      phoneValid: isValid,
      phoneError: phone.length > 0 && !isValid
    });
    return isValid;
  },

  // 输入身份证号
  inputIdCard: function(e) {
    this.setData({
      idCard: e.detail.value
    });
    this.validateIdCard();
  },

  // 验证身份证号
  validateIdCard: function() {
    const util = require('@/utils/util');
    const idCard = this.data.idCard.trim();
    const isValid = util.validateIdCard(idCard);
    this.setData({
      idCardValid: isValid,
      idCardError: idCard.length > 0 && !isValid
    });
    return isValid;
  },

  // 输入员工编号
  inputEmployeeId: function(e) {
    this.setData({
      employeeId: e.detail.value
    });
    this.validateEmployeeId();
  },

  // 验证员工编号
  validateEmployeeId: function() {
    const employeeId = this.data.employeeId.trim();
    const isValid = employeeId.length >= 2;
    this.setData({
      employeeIdValid: isValid,
      employeeIdError: employeeId.length > 0 && !isValid
    });
    return isValid;
  },

  // 选择部门
  bindDepartmentChange: function(e) {
    const index = e.detail.value;
    this.setData({
      department: this.data.departments[index],
      departmentIndex: index,
      departmentValid: true,
      departmentError: false
    });
  },

  // 选择职位
  bindPositionChange: function(e) {
    const index = e.detail.value;
    this.setData({
      position: this.data.positions[index],
      positionIndex: index,
      positionValid: true,
      positionError: false
    });
  },

  // 选择入职日期
  bindEntryDateChange: function(e) {
    this.setData({
      entryDate: e.detail.value
    });
  },

  // 选择状态
  bindStatusChange: function(e) {
    const index = e.detail.value;
    this.setData({
      status: this.data.statuses[index],
      statusIndex: index
    });
  },

  // 输入邮箱
  inputEmail: function(e) {
    this.setData({
      email: e.detail.value
    });
  },

  // 输入地址
  inputAddress: function(e) {
    this.setData({
      address: e.detail.value
    });
  },

  // 输入紧急联系人
  inputEmergencyContact: function(e) {
    this.setData({
      emergencyContact: e.detail.value
    });
  },

  // 输入紧急联系电话
  inputEmergencyPhone: function(e) {
    this.setData({
      emergencyPhone: e.detail.value
    });
  },

  // 选择学历
  bindEducationChange: function(e) {
    const index = e.detail.value;
    this.setData({
      education: this.data.educations[index],
      educationIndex: index
    });
  },

  // 输入专业
  inputMajor: function(e) {
    this.setData({
      major: e.detail.value
    });
  },

  // 输入技能
  inputSkills: function(e) {
    this.setData({
      skills: e.detail.value
    });
  },

  // 输入证书
  inputCertificates: function(e) {
    this.setData({
      certificates: e.detail.value
    });
  },

  // 选择绩效
  bindPerformanceChange: function(e) {
    const index = e.detail.value;
    this.setData({
      performance: this.data.performances[index],
      performanceIndex: index
    });
  },

  // 输入薪资
  inputSalary: function(e) {
    this.setData({
      salary: e.detail.value
    });
  },

  // 输入备注
  inputNotes: function(e) {
    this.setData({
      notes: e.detail.value
    });
  },

  // 上传工作证照片
  uploadEmployeeCard: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取图片临时路径
        const tempFilePath = res.tempFilePaths[0];

        // 显示上传中
        wx.showLoading({
          title: '上传中...',
        });

        // 模拟上传过程
        setTimeout(() => {
          this.setData({
            employeeCardPhotoPath: tempFilePath
          });
          wx.hideLoading();
        }, 1000);
      }
    });
  },

  // 上传人脸照片
  uploadFacePhoto: function() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 获取图片临时路径
        const tempFilePath = res.tempFilePaths[0];

        // 显示上传中
        wx.showLoading({
          title: '上传中...',
        });

        // 模拟上传过程
        setTimeout(() => {
          this.setData({
            facePhotoPath: tempFilePath
          });
          wx.hideLoading();
        }, 1000);
      }
    });
  },

  // 提交表单
  submitForm: function() {
    // 验证表单
    let isValid = true;

    // 验证姓名
    if (!this.validateName()) {
      isValid = false;
    }

    // 验证手机号
    if (!this.validatePhone()) {
      isValid = false;
    }

    // 验证身份证号
    if (!this.validateIdCard()) {
      isValid = false;
    }

    // 验证员工编号
    if (!this.validateEmployeeId()) {
      isValid = false;
    }

    // 验证部门
    if (!this.data.department) {
      this.setData({
        departmentError: true
      });
      isValid = false;
    }

    // 验证职位
    if (!this.data.position) {
      this.setData({
        positionError: true
      });
      isValid = false;
    }

    // 验证工作证照片
    if (!this.data.employeeCardPhotoPath) {
      this.showError('请上传工作证照片');
      isValid = false;
    }

    if (!isValid) {
      return;
    }

    // 开始提交
    this.setData({
      isSubmitting: true
    });

    // 模拟提交
    setTimeout(() => {
      this.setData({
        isSubmitting: false
      });

      // 显示成功提示
      wx.showToast({
        title: this.data.mode === 'add' ? '添加成功' : '更新成功',
        icon: 'success',
        duration: 2000,
        complete: () => {
          // 设置需要刷新员工列表和详情的标志
          wx.setStorageSync('staffDataNeedRefresh', true);
          if (this.data.mode === 'edit') {
            wx.setStorageSync('staffDetailNeedRefresh', true);
          }

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      });
    }, 2000);
  },

  // 显示错误信息
  showError: function(msg) {
    this.setData({
      errorMsg: msg
    });
    setTimeout(() => {
      this.setData({
        errorMsg: ''
      });
    }, 3000);
  },

  // 取消编辑
  cancelEdit: function() {
    wx.navigateBack();
  }
})
