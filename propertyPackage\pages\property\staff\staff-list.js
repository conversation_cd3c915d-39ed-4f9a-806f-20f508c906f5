// pages/property/staff/staff-list.js
const util = require('@/utils/util.js')
const propertyApi = require('@/api/propertyApi.js')

Page({
  data: {
    darkMode: false,
    staffList: [],
    filteredStaffList: [],
    searchText: '',
    isLoading: true,
    isEmpty: false,

    // 分页参数
    pageNum: 1,
    pageSize: 10,
    total: 0,
    hasMore: true,

    // 筛选条件
    filterVisible: false,
    departments: ['全部'], // 从组织列表动态获取
    selectedDepartment: '全部',
    positions: ['全部'], // 从职位列表动态获取
    selectedPosition: '全部',
    genders: ['全部', '男', '女'],
    selectedGender: '全部',
    ageRanges: ['全部', '20岁以下', '20-30岁', '30-40岁', '40-50岁', '50岁以上'],
    selectedAgeRange: '全部',

    // 排序
    sortOptions: ['姓名', '入职时间', '组织'],
    selectedSortOption: '入职时间',
    isAscending: false,

    // 员工状态字典
    personStatusDict: [],
    // 员工性别字典
    genderDict: [],

    // 组织和职位数据
    orgList: [],
    positionList: [],

    // API URL前缀
    apiUrl: ''
  },

  onLoad: function () {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode,
        apiUrl: wx.getStorageSync('apiUrl') || ''
      })
    } else {
      this.setData({
        apiUrl: wx.getStorageSync('apiUrl') || ''
      })
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '员工管理'
    });

    // 初始化数据
    this.initializeData();
  },

  // 初始化数据
  initializeData: function() {
    // 获取员工状态字典
    const personStatus = util.getDictByNameEn('person_status');
    if (personStatus && personStatus.length > 0 && personStatus[0].children) {
      this.setData({
        personStatusDict: personStatus[0].children
      });
    }

    // 获取性别字典
    const genderDict = util.getDictByNameEn('gender');
    if (genderDict && genderDict.length > 0 && genderDict[0].children) {
      this.setData({
        genderDict: genderDict[0].children
      });
    }

    console.log('personStatusDict', this.data.personStatusDict);
    console.log('genderDict', this.data.genderDict);

    // 并行加载基础数据
    Promise.all([
      this.loadOrgTree(),
      this.loadPositions()
    ]).then(() => {
      // 基础数据加载完成后，加载员工数据
      this.loadStaffData();
    }).catch(err => {
      console.error('初始化数据失败:', err);
      // 即使基础数据加载失败，也要尝试加载员工数据
      this.loadStaffData();
    });
  },

  // 获取组织列表
  loadOrgTree: function() {
    return new Promise((resolve, reject) => {
      propertyApi.getOrgTree().then(res => {
        console.log('组织列表数据:', res);

        if (res && res.list && Array.isArray(res.list)) {
          const orgList = res.list;

          // 提取所有部门名称用于筛选
          const departments = ['全部'];
          const extractDepartments = (orgs) => {
            orgs.forEach(org => {
              if (org.type === 'dept' && org.orgName) {
                departments.push(org.orgName);
              }
              if (org.children && org.children.length > 0) {
                extractDepartments(org.children);
              }
            });
          };

          extractDepartments(orgList);

          this.setData({
            orgList: orgList,
            departments: departments
          });

          console.log('部门列表:', departments);
          resolve(orgList);
        } else {
          console.log('组织列表数据为空');
          resolve([]);
        }
      }).catch(err => {
        console.error('获取组织列表失败:', err);
        reject(err);
      });
    });
  },

  // 获取职位列表
  loadPositions: function() {
    return new Promise((resolve, reject) => {
      const commApi = require('@/api/commApi.js');
      const params = {
        pageNum: 1,
        pageSize: 100 // 获取所有职位
      };

      commApi.getPositionPage(params).then(res => {
        console.log('职位列表数据:', res);

        if (res && res.list && Array.isArray(res.list)) {
          const positionList = res.list;
          const positions = ['全部'];

          positionList.forEach(position => {
            if (position.positionName) {
              positions.push(position.positionName);
            }
          });

          this.setData({
            positionList: positionList,
            positions: positions
          });

          console.log('职位列表:', positions);
          resolve(positionList);
        } else {
          console.log('职位列表数据为空');
          resolve([]);
        }
      }).catch(err => {
        console.error('获取职位列表失败:', err);
        reject(err);
      });
    });
  },



  // 加载员工数据
  loadStaffData: function (isLoadMore = false) {
    if (!isLoadMore) {
      this.setData({
        isLoading: true,
        pageNum: 1
      });
    }

    const params = {
      pageNum: this.data.pageNum,
      pageSize: this.data.pageSize,
      personName: this.data.searchText || '',
      phone: '',
      personNumber: ''
    };

    console.log('加载员工数据，参数:', params);

    propertyApi.getPersonList(params).then(res => {
      console.log('员工列表API响应:', res);

      if (res && res.list && Array.isArray(res.list)) {
        const rawStaffList = res.list;
        const total = res.total || 0;

        // 转换数据格式
        const formattedStaffList = rawStaffList.map(item => this.formatStaffData(item));

        // 处理分页数据
        let staffList;
        if (isLoadMore) {
          staffList = [...this.data.staffList, ...formattedStaffList];
        } else {
          staffList = formattedStaffList;
        }

        const hasMore = staffList.length < total;

        this.setData({
          staffList: staffList,
          filteredStaffList: staffList,
          total: total,
          hasMore: hasMore,
          isLoading: false,
          isEmpty: staffList.length === 0,
          pageNum: this.data.pageNum + 1
        });

        console.log(`员工数据加载完成，共${staffList.length}条，总计${total}条`);

        // 应用筛选和排序
        this.applyFiltersAndSort();
      } else {
        this.setData({
          staffList: [],
          filteredStaffList: [],
          total: 0,
          hasMore: false,
          isLoading: false,
          isEmpty: true
        });
        console.log('员工列表数据为空');
      }
    }).catch(err => {
      console.error('获取员工列表失败:', err);
      this.setData({
        isLoading: false,
        isEmpty: this.data.staffList.length === 0
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  // 格式化员工数据
  formatStaffData: function(rawData) {
    // 获取组织名称（注意：不是部门，是组织）
    const orgName = this.getOrgName(rawData.orgId);

    // 获取职位名称
    const positionName = this.getPositionName(rawData.positionId);

    // 获取状态显示文本
    const statusText = this.getStatusText(rawData.status);

    // 获取性别显示文本
    const genderText = this.getGenderText(rawData.gender);

    // 处理员工照片路径（注意：media是员工照片）
    const employeePhoto = rawData.media ?
      `${this.data.apiUrl}/common-api/v1/file/${rawData.media}` :
      '';

    return {
      id: rawData.id,
      name: rawData.personName || '',
      gender: genderText,
      age: rawData.age || 0,
      phone: rawData.phone || '',
      idCard: rawData.idCard || '',
      employeeId: rawData.personNumber || '',
      organization: orgName, // 改为组织名称
      position: positionName,
      entryDate: rawData.entryTime ? rawData.entryTime.split(' ')[0] : '', // 只取日期部分
      employeePhoto: employeePhoto, // 员工照片
      status: statusText,
      statusCode: rawData.status, // 保留原始状态码用于筛选
      genderCode: rawData.gender, // 保留原始性别码用于筛选
      orgId: rawData.orgId,
      positionId: rawData.positionId,
      salary: rawData.salary || 0,
      email: rawData.email || '',
      address: rawData.address || ''
    };
  },

  // 根据组织ID获取组织名称
  getOrgName: function(orgId) {
    if (!orgId || !this.data.orgList.length) return '未知组织';

    const findOrg = (orgs) => {
      for (let org of orgs) {
        if (org.id === orgId) {
          return org.orgName;
        }
        if (org.children && org.children.length > 0) {
          const found = findOrg(org.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findOrg(this.data.orgList) || '未知组织';
  },

  // 根据职位ID获取职位名称
  getPositionName: function(positionId) {
    if (!positionId || !this.data.positionList.length) return '未知职位';

    const position = this.data.positionList.find(p => p.id === positionId);
    return position ? position.positionName : '未知职位';
  },

  // 根据状态码获取状态文本
  getStatusText: function(statusCode) {
    if (!statusCode || !this.data.personStatusDict.length) return '未知状态';

    const status = this.data.personStatusDict.find(s => s.nameEn === statusCode);
    return status ? status.nameCn : '未知状态';
  },

  // 根据性别码获取性别文本
  getGenderText: function(genderCode) {
    if (!genderCode || !this.data.genderDict.length) return '未知';

    const gender = this.data.genderDict.find(g => g.nameEn === genderCode);
    return gender ? gender.nameCn : '未知';
  },

  // 搜索员工
  onSearchInput: function (e) {
    const searchText = e.detail.value;
    this.setData({
      searchText: searchText,
      pageNum: 1
    });

    // 重新加载数据
    this.loadStaffData();
  },

  // 清除搜索
  clearSearch: function () {
    this.setData({
      searchText: '',
      pageNum: 1
    });

    // 重新加载数据
    this.loadStaffData();
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    console.log('下拉刷新员工列表');
    this.setData({
      pageNum: 1
    });

    this.loadStaffData().then(() => {
      wx.stopPullDownRefresh();
    }).catch(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.isLoading) {
      console.log('加载更多员工数据');
      this.loadStaffData(true);
    }
  },

  // 显示筛选面板
  showFilter: function () {
    this.setData({
      filterVisible: true
    });
  },

  // 隐藏筛选面板
  hideFilter: function () {
    this.setData({
      filterVisible: false
    });
  },

  // 选择部门
  selectDepartment: function (e) {
    this.setData({
      selectedDepartment: this.data.departments[e.currentTarget.dataset.index]
    });
  },

  // 选择职位
  selectPosition: function (e) {
    this.setData({
      selectedPosition: this.data.positions[e.currentTarget.dataset.index]
    });
  },

  // 选择性别
  selectGender: function (e) {
    this.setData({
      selectedGender: this.data.genders[e.currentTarget.dataset.index]
    });
  },

  // 选择年龄范围
  selectAgeRange: function (e) {
    this.setData({
      selectedAgeRange: this.data.ageRanges[e.currentTarget.dataset.index]
    });
  },

  // 应用筛选
  applyFilter: function () {
    this.applyFiltersAndSort();
    this.hideFilter();
  },

  // 重置筛选
  resetFilter: function () {
    this.setData({
      selectedDepartment: '全部',
      selectedPosition: '全部',
      selectedGender: '全部',
      selectedAgeRange: '全部'
    });
    this.applyFiltersAndSort();
  },

  // 选择排序选项
  selectSortOption: function (e) {
    const option = this.data.sortOptions[e.currentTarget.dataset.index];

    if (this.data.selectedSortOption === option) {
      // 如果选择了相同的排序选项，则切换排序方向
      this.setData({
        isAscending: !this.data.isAscending
      });
    } else {
      // 如果选择了不同的排序选项，则设置新的排序选项并默认为升序
      this.setData({
        selectedSortOption: option,
        isAscending: true
      });
    }

    this.applyFiltersAndSort();
  },

  // 应用筛选和排序（不包括搜索，搜索通过API进行）
  applyFiltersAndSort: function () {
    let filtered = [...this.data.staffList];

    // 搜索已经通过API进行，这里不再处理搜索逻辑

    // 应用组织筛选
    if (this.data.selectedDepartment !== '全部') {
      filtered = filtered.filter(staff => staff.organization === this.data.selectedDepartment);
    }

    // 应用职位筛选
    if (this.data.selectedPosition !== '全部') {
      filtered = filtered.filter(staff => staff.position === this.data.selectedPosition);
    }

    // 应用性别筛选
    if (this.data.selectedGender !== '全部') {
      filtered = filtered.filter(staff => staff.gender === this.data.selectedGender);
    }

    // 应用年龄范围筛选
    if (this.data.selectedAgeRange !== '全部') {
      switch (this.data.selectedAgeRange) {
        case '20岁以下':
          filtered = filtered.filter(staff => staff.age < 20);
          break;
        case '20-30岁':
          filtered = filtered.filter(staff => staff.age >= 20 && staff.age <= 30);
          break;
        case '30-40岁':
          filtered = filtered.filter(staff => staff.age > 30 && staff.age <= 40);
          break;
        case '40-50岁':
          filtered = filtered.filter(staff => staff.age > 40 && staff.age <= 50);
          break;
        case '50岁以上':
          filtered = filtered.filter(staff => staff.age > 50);
          break;
      }
    }

    // 应用排序
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (this.data.selectedSortOption) {
        case '姓名':
          comparison = a.name.localeCompare(b.name, 'zh');
          break;
        case '入职时间':
          comparison = new Date(a.entryDate) - new Date(b.entryDate);
          break;
        case '组织':
          comparison = a.organization.localeCompare(b.organization, 'zh');
          break;
      }

      return this.data.isAscending ? comparison : -comparison;
    });

    this.setData({
      filteredStaffList: filtered,
      isEmpty: filtered.length === 0
    });
  },

  // 查看员工详情
  viewStaffDetail: function (e) {
    const staffId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `./staff-detail?id=${staffId}`
    });
  },

  // 添加新员工
  addNewStaff: function () {
    wx.navigateTo({
      url: './staff-edit?mode=add'
    });
  },

  // 查看员工统计
  viewStaffStats: function () {
    wx.navigateTo({
      url: './staff-stats'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadStaffData();
    wx.stopPullDownRefresh();
  },

  // 页面显示时刷新数据
  onShow: function () {
    // 检查是否需要刷新数据
    const needRefresh = wx.getStorageSync('staffDataNeedRefresh');
    if (needRefresh) {
      this.loadStaffData();
      wx.removeStorageSync('staffDataNeedRefresh');
    }
  }
})
