// pages/property/staff/staff-list.js
const util = require('@/utils/util.js')
const propertyApi = require('@/api/propertyApi.js')

Page({
  data: {
    darkMode: false,
    staffList: [],
    filteredStaffList: [],
    searchText: '',
    isLoading: true,
    isEmpty: false,

    // 筛选条件
    filterVisible: false,
    departments: ['全部', '物业管理部', '客服部', '工程部', '保安部', '保洁部', '绿化部', '财务部'],
    selectedDepartment: '全部',
    positions: ['全部', '经理', '主管', '工程师', '客服专员', '保安', '保洁员', '绿化工', '财务专员'],
    selectedPosition: '全部',
    genders: ['全部', '男', '女'],
    selectedGender: '全部',
    ageRanges: ['全部', '20岁以下', '20-30岁', '30-40岁', '40-50岁', '50岁以上'],
    selectedAgeRange: '全部',

    // 排序
    sortOptions: ['姓名', '入职时间', '部门'],
    selectedSortOption: '入职时间',
    isAscending: false,


    //员工状态字典
    personStatusDict:[]
  },

  onLoad: function () {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '员工管理'
    });

    //获取员工状态字典
    var personStatus= util.getDictByNameEn('person_status');
    if (personStatus && personStatus.length > 0 && personStatus[0].children) {
      this.setData({
        personStatusDict: personStatus[0].children
      });
    }
   console.log('personStatusDict',this.data.personStatusDict)

    //获取组织列表
    this.getOrgTree()



    // 加载员工数据
    this.loadStaffData();


  },

  getOrgTree()
  {
    propertyApi.getOrgTree().then(res => {
      console.log(res)
  })

  },


  // 加载员工数据
  loadStaffData: function () {
    this.setData({
      isLoading: true
    });

    var params = {
      pageNum: 1,
      pageSize: 10,
      personName: '',
      phone: '',
      personNumber: ''
    }

    propertyApi.getPersonList(params).then(res => {
        console.log(res)
    })



    // 模拟从服务器获取员工数据
    setTimeout(() => {
      // 模拟数据
      const staffList = [
        {
          id: '001',
          name: '张三',
          gender: '男',
          age: 35,
          phone: '13800138001',
          idCard: '110101198505079876',
          employeeId: 'ZH001',
          department: '物业管理部',
          position: '经理',
          entryDate: '2020-01-15',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职'
        },
        {
          id: '002',
          name: '李四',
          gender: '女',
          age: 28,
          phone: '13900139002',
          idCard: '110101199201234567',
          employeeId: 'ZH002',
          department: '客服部',
          position: '客服专员',
          entryDate: '2021-03-20',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职'
        },
        {
          id: '003',
          name: '王五',
          gender: '男',
          age: 42,
          phone: '13700137003',
          idCard: '110101197901234567',
          employeeId: 'ZH003',
          department: '工程部',
          position: '工程师',
          entryDate: '2019-05-10',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职'
        },
        {
          id: '004',
          name: '赵六',
          gender: '女',
          age: 31,
          phone: '13600136004',
          idCard: '110101199001234567',
          employeeId: 'ZH004',
          department: '财务部',
          position: '财务专员',
          entryDate: '2022-01-05',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职'
        },
        {
          id: '005',
          name: '钱七',
          gender: '男',
          age: 45,
          phone: '13500135005',
          idCard: '110101197601234567',
          employeeId: 'ZH005',
          department: '保安部',
          position: '主管',
          entryDate: '2018-08-18',
          employeeCardPhoto: '/images/default-avatar.svg',
          facePhoto: '/images/default-avatar.svg',
          status: '在职'
        }
      ];

      this.setData({
        staffList: staffList,
        filteredStaffList: staffList,
        isLoading: false,
        isEmpty: staffList.length === 0
      });

      // 应用筛选和排序
      this.applyFiltersAndSort();
    }, 1000);
  },

  // 搜索员工
  onSearchInput: function (e) {
    this.setData({
      searchText: e.detail.value
    });
    this.applyFiltersAndSort();
  },

  // 清除搜索
  clearSearch: function () {
    this.setData({
      searchText: ''
    });
    this.applyFiltersAndSort();
  },

  // 显示筛选面板
  showFilter: function () {
    this.setData({
      filterVisible: true
    });
  },

  // 隐藏筛选面板
  hideFilter: function () {
    this.setData({
      filterVisible: false
    });
  },

  // 选择部门
  selectDepartment: function (e) {
    this.setData({
      selectedDepartment: this.data.departments[e.currentTarget.dataset.index]
    });
  },

  // 选择职位
  selectPosition: function (e) {
    this.setData({
      selectedPosition: this.data.positions[e.currentTarget.dataset.index]
    });
  },

  // 选择性别
  selectGender: function (e) {
    this.setData({
      selectedGender: this.data.genders[e.currentTarget.dataset.index]
    });
  },

  // 选择年龄范围
  selectAgeRange: function (e) {
    this.setData({
      selectedAgeRange: this.data.ageRanges[e.currentTarget.dataset.index]
    });
  },

  // 应用筛选
  applyFilter: function () {
    this.applyFiltersAndSort();
    this.hideFilter();
  },

  // 重置筛选
  resetFilter: function () {
    this.setData({
      selectedDepartment: '全部',
      selectedPosition: '全部',
      selectedGender: '全部',
      selectedAgeRange: '全部'
    });
  },

  // 选择排序选项
  selectSortOption: function (e) {
    const option = this.data.sortOptions[e.currentTarget.dataset.index];

    if (this.data.selectedSortOption === option) {
      // 如果选择了相同的排序选项，则切换排序方向
      this.setData({
        isAscending: !this.data.isAscending
      });
    } else {
      // 如果选择了不同的排序选项，则设置新的排序选项并默认为升序
      this.setData({
        selectedSortOption: option,
        isAscending: true
      });
    }

    this.applyFiltersAndSort();
  },

  // 应用筛选和排序
  applyFiltersAndSort: function () {
    let filtered = [...this.data.staffList];

    // 应用搜索
    if (this.data.searchText) {
      const searchText = this.data.searchText.toLowerCase();
      filtered = filtered.filter(staff =>
        staff.name.toLowerCase().includes(searchText) ||
        staff.employeeId.toLowerCase().includes(searchText)
      );
    }

    // 应用部门筛选
    if (this.data.selectedDepartment !== '全部') {
      filtered = filtered.filter(staff => staff.department === this.data.selectedDepartment);
    }

    // 应用职位筛选
    if (this.data.selectedPosition !== '全部') {
      filtered = filtered.filter(staff => staff.position === this.data.selectedPosition);
    }

    // 应用性别筛选
    if (this.data.selectedGender !== '全部') {
      filtered = filtered.filter(staff => staff.gender === this.data.selectedGender);
    }

    // 应用年龄范围筛选
    if (this.data.selectedAgeRange !== '全部') {
      switch (this.data.selectedAgeRange) {
        case '20岁以下':
          filtered = filtered.filter(staff => staff.age < 20);
          break;
        case '20-30岁':
          filtered = filtered.filter(staff => staff.age >= 20 && staff.age <= 30);
          break;
        case '30-40岁':
          filtered = filtered.filter(staff => staff.age > 30 && staff.age <= 40);
          break;
        case '40-50岁':
          filtered = filtered.filter(staff => staff.age > 40 && staff.age <= 50);
          break;
        case '50岁以上':
          filtered = filtered.filter(staff => staff.age > 50);
          break;
      }
    }

    // 应用排序
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (this.data.selectedSortOption) {
        case '姓名':
          comparison = a.name.localeCompare(b.name, 'zh');
          break;
        case '入职时间':
          comparison = new Date(a.entryDate) - new Date(b.entryDate);
          break;
        case '部门':
          comparison = a.department.localeCompare(b.department, 'zh');
          break;
      }

      return this.data.isAscending ? comparison : -comparison;
    });

    this.setData({
      filteredStaffList: filtered,
      isEmpty: filtered.length === 0
    });
  },

  // 查看员工详情
  viewStaffDetail: function (e) {
    const staffId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `./staff-detail?id=${staffId}`
    });
  },

  // 添加新员工
  addNewStaff: function () {
    wx.navigateTo({
      url: './staff-edit?mode=add'
    });
  },

  // 查看员工统计
  viewStaffStats: function () {
    wx.navigateTo({
      url: './staff-stats'
    });
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadStaffData();
    wx.stopPullDownRefresh();
  },

  // 页面显示时刷新数据
  onShow: function () {
    // 检查是否需要刷新数据
    const needRefresh = wx.getStorageSync('staffDataNeedRefresh');
    if (needRefresh) {
      this.loadStaffData();
      wx.removeStorageSync('staffDataNeedRefresh');
    }
  }
})
