/**
 * WebSocket管理工具类
 * 专注于WebSocket连接生命周期管理，与登录系统解耦
 *
 * 核心职责：
 * 1. WebSocket连接状态管理（CONNECTING, OPEN, CLOSING, CLOSED）
 * 2. Token状态检测和连接控制
 * 3. 断线重连机制（指数退避算法）
 * 4. 心跳保活机制
 * 5. 消息队列缓冲
 * 6. 错误处理和状态同步
 *
 * 设计原则：
 * - 只负责WebSocket连接管理，不处理用户登录
 * - 通过wx.getStorageSync('access_token')获取token状态
 * - 依赖现有request.js的token管理机制
 */

const WebSocketConfig = require('./websocket-config');

class WebSocketManager {
  constructor() {
    // 加载配置
    this.config = {
      server: WebSocketConfig.server,
      reconnect: WebSocketConfig.reconnect,
      heartbeat: WebSocketConfig.heartbeat,
      message: WebSocketConfig.message,
      logging: WebSocketConfig.logging
    };

    // 验证配置
    const validation = WebSocketConfig.validateConfig(this.config);
    if (!validation.valid) {
      console.error('WebSocketManager: 配置验证失败', validation.errors);
      throw new Error('WebSocket配置无效: ' + validation.errors.join(', '));
    }

    // WebSocket连接实例
    this.socketTask = null;

    // 连接状态枚举
    this.READY_STATE = {
      CONNECTING: 0,
      OPEN: 1,
      CLOSING: 2,
      CLOSED: 3
    };

    // 当前连接状态
    this.readyState = this.READY_STATE.CLOSED;

    // 重连状态管理
    this.reconnectState = {
      maxAttempts: this.config.reconnect.maxAttempts,
      currentAttempts: 0,
      baseDelay: this.config.reconnect.baseDelay,
      maxDelay: this.config.reconnect.maxDelay,
      isReconnecting: false,
      useExponentialBackoff: this.config.reconnect.useExponentialBackoff,
      reconnectTimer: null
    };

    // 心跳状态管理
    this.heartbeatState = {
      interval: this.config.heartbeat.interval,
      timeout: this.config.heartbeat.timeout,
      timer: null,
      timeoutTimer: null,
      missedCount: 0,
      maxMissed: this.config.heartbeat.maxMissed,
      isActive: false
    };

    // Token状态管理
    this.tokenState = {
      checkInterval: 10000,  // 10秒检查一次token状态
      checkTimer: null,
      lastToken: null,
      isWaitingForToken: false
    };

    // 消息队列
    this.messageQueue = [];
    this.maxQueueLength = this.config.message.maxQueueLength;

    // 事件监听器
    this.eventListeners = {
      open: [],
      message: [],
      close: [],
      error: [],
      reconnect: [],
      tokenChange: [],
      messageResponse: [],    // 消息发送响应
      privateMessage: []      // 接收到的私聊消息
    };

    // 消息主题常量
    this.TOPICS = this.config.message.topics;

    // 初始化标志
    this.isInitialized = false;

    // 绑定方法上下文
    this.handleOpen = this.handleOpen.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
    this.checkTokenStatus = this.checkTokenStatus.bind(this);

    // 初始化日志配置
    this.logging = this.config.logging;

    this.log('info', 'WebSocketManager初始化完成');
  }

  /**
   * 日志记录方法
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {Object} data - 附加数据
   */
  log(level, message, data = null) {
    if (!this.logging.verbose && level === 'debug') return;

    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.logging.level);
    const messageLevelIndex = levels.indexOf(level);

    if (messageLevelIndex >= currentLevelIndex) {
      const logMessage = `WebSocketManager: ${message}`;

      switch (level) {
        case 'debug':
        case 'info':
          console.log(logMessage, data);
          break;
        case 'warn':
          console.warn(logMessage, data);
          break;
        case 'error':
          console.error(logMessage, data);
          break;
      }
    }
  }

  /**
   * 初始化WebSocket管理器
   * 专注于连接生命周期管理，不处理登录逻辑
   */
  init() {
    if (this.isInitialized) {
      this.log('info', '已经初始化，跳过重复初始化');
      return;
    }

    this.log('info', '开始初始化WebSocket管理器');

    // 启动token状态监控
    this.startTokenMonitoring();

    // 监听小程序生命周期
    this.setupAppLifecycleListeners();

    // 设置request.js的token变化监听
    this.setupRequestTokenListener();

    // 立即检查token状态并尝试连接
    this.checkTokenStatus();

    this.isInitialized = true;
    this.log('info', 'WebSocket管理器初始化完成');
  }

  /**
   * 启动token状态监控
   * 定期检查token状态变化
   */
  startTokenMonitoring() {
    // 清除现有定时器
    if (this.tokenState.checkTimer) {
      clearInterval(this.tokenState.checkTimer);
    }

    // 启动定期检查
    this.tokenState.checkTimer = setInterval(() => {
      this.checkTokenStatus();
    }, this.tokenState.checkInterval);

    this.log('debug', '启动token状态监控', { interval: this.tokenState.checkInterval });
  }

  /**
   * 检查token是否过期
   * @param {string} token - 当前token
   * @param {string} expireTime - token过期时间戳
   * @returns {boolean} 是否过期
   */
  isTokenExpired(token, expireTime) {
    if (!token) return true;

    if (!expireTime) {
      // 如果没有过期时间信息，认为token有效
      return false;
    }

    const now = Date.now();
    const expire = parseInt(expireTime);
    const bufferTime = 5 * 60 * 1000; // 5分钟缓冲时间

    return now >= (expire - bufferTime);
  }

  /**
   * 设置小程序生命周期监听
   * 监听前台后台切换，处理锁屏等场景
   */
  setupAppLifecycleListeners() {
    // 监听小程序显示（从后台进入前台）
    wx.onAppShow(() => {
      this.log('info', '小程序从后台进入前台');
      this.handleAppShow();
    });

    // 监听小程序隐藏（进入后台）
    wx.onAppHide(() => {
      this.log('info', '小程序进入后台');
      this.handleAppHide();
    });

    this.log('info', '小程序生命周期监听已设置');
  }

  /**
   * 设置request.js的token变化监听
   * 监听token刷新成功事件
   */
  setupRequestTokenListener() {
    try {
      const request = require('./request.js');

      // 如果request模块有notifyWebSocketTokenChange方法，说明支持通知
      if (typeof request.notifyWebSocketTokenChange === 'function') {
        this.log('info', '设置request.js token变化监听');

        // 设置全局监听器，当token变化时会被调用
        const originalNotify = request.notifyWebSocketTokenChange;
        request.notifyWebSocketTokenChange = (tokenData) => {
          this.log('info', '收到request.js的token变化通知', { hasToken: !!tokenData.token });

          // 调用原始方法
          if (typeof originalNotify === 'function') {
            originalNotify(tokenData);
          }

          // 处理token变化
          this.handleRequestTokenChange(tokenData);
        };
      } else {
        this.log('warn', 'request模块不支持token变化通知');
      }
    } catch (error) {
      this.log('error', '设置request.js token监听失败', error);
    }
  }

  /**
   * 处理来自request.js的token变化通知
   * @param {Object} tokenData - token数据
   */
  handleRequestTokenChange(tokenData) {
    this.log('info', '处理request.js token变化', tokenData);

    // 立即检查token状态
    setTimeout(() => {
      this.checkTokenStatus();
    }, 500); // 延迟500ms确保token已保存到storage
  }

  /**
   * 处理小程序从后台进入前台
   */
  handleAppShow() {
    this.log('info', '处理小程序前台显示');

    // 立即检查token状态
    this.checkTokenStatus();

    // 如果连接已断开，尝试重连
    if (this.readyState === this.READY_STATE.CLOSED) {
      const token = wx.getStorageSync('access_token');
      if (token && !this.isTokenExpired(token, wx.getStorageSync('token_expire_time'))) {
        this.log('info', '小程序前台显示，尝试重新连接');
        this.startReconnect();
      }
    }
  }

  /**
   * 处理小程序进入后台
   */
  handleAppHide() {
    this.log('info', '处理小程序后台隐藏');
    // 保持连接，不主动断开
    // WebSocket会在后台保持一段时间，系统会自动管理
  }

  /**
   * 检查token状态
   * 根据token状态决定连接行为
   */
  checkTokenStatus() {
    const currentToken = wx.getStorageSync('access_token');
    const tokenExpireTime = wx.getStorageSync('token_expire_time');
    const tokenChanged = currentToken !== this.tokenState.lastToken;

    // 检查token是否过期
    const isTokenExpired = this.isTokenExpired(currentToken, tokenExpireTime);

    this.log('debug', '检查token状态', {
      hasToken: !!currentToken,
      tokenChanged,
      isTokenExpired,
      isWaiting: this.tokenState.isWaitingForToken,
      readyState: this.readyState
    });

    // 更新token状态
    this.tokenState.lastToken = currentToken;

    if (!currentToken) {
      // 无token状态处理
      this.handleNoToken();
    } else if (isTokenExpired) {
      // token过期，尝试刷新
      this.handleTokenExpired();
    } else if (tokenChanged && this.tokenState.isWaitingForToken) {
      // token获取完成，结束等待状态
      this.handleTokenAvailable(currentToken);
    } else if (currentToken && this.readyState === this.READY_STATE.CLOSED && !this.reconnectState.isReconnecting) {
      // 有token且连接已关闭，尝试建立连接
      this.handleTokenAvailable(currentToken);
    }

    // 触发token变化事件
    if (tokenChanged) {
      this.emit('tokenChange', { token: currentToken, hasToken: !!currentToken });
    }
  }

  /**
   * 处理无token状态
   * 进入等待模式，停止连接尝试
   */
  handleNoToken() {
    if (!this.tokenState.isWaitingForToken) {
      this.log('info', '检测到无token状态，进入等待模式');
      this.tokenState.isWaitingForToken = true;

      // 关闭现有连接
      if (this.readyState !== this.READY_STATE.CLOSED) {
        this.close(1000, 'Token not available');
      }

      // 停止重连机制
      this.stopReconnect();

      // 停止心跳
      this.stopHeartbeat();
    }
  }

  /**
   * 处理token过期状态
   * 尝试刷新token，成功后重连
   */
  handleTokenExpired() {
    this.log('info', '检测到token过期，尝试刷新token');

    // 关闭现有连接
    if (this.readyState !== this.READY_STATE.CLOSED) {
      this.close(1000, 'Token expired');
    }

    // 停止重连机制
    this.stopReconnect();

    // 停止心跳
    this.stopHeartbeat();

    // 尝试刷新token
    this.refreshTokenAndReconnect();
  }

  /**
   * 刷新token并重连
   */
  refreshTokenAndReconnect() {
    // 检查是否有request模块可用
    try {
      const request = require('./request.js');

      if (typeof request.refreshToken === 'function') {
        this.log('info', '开始刷新token');

        request.refreshToken()
          .then(() => {
            this.log('info', 'Token刷新成功，准备重连');

            // 重新检查token状态并尝试连接
            setTimeout(() => {
              this.checkTokenStatus();
            }, 1000); // 延迟1秒确保token已保存
          })
          .catch((error) => {
            this.log('error', 'Token刷新失败', error);

            // 刷新失败，进入等待状态
            this.handleNoToken();
          });
      } else {
        this.log('warn', 'request模块不支持token刷新，进入等待状态');
        this.handleNoToken();
      }
    } catch (error) {
      this.log('error', '无法加载request模块进行token刷新', error);
      this.handleNoToken();
    }
  }

  /**
   * 处理token可用状态
   * 尝试建立或恢复连接
   */
  handleTokenAvailable(token) {
    this.log('info', '检测到token可用，尝试建立连接', { hasToken: !!token });

    // 结束等待状态
    this.tokenState.isWaitingForToken = false;

    // 如果当前没有连接或连接已关闭，尝试建立连接
    if (this.readyState === this.READY_STATE.CLOSED && !this.reconnectState.isReconnecting) {
      this.connect().catch(error => {
        this.log('error', 'token可用时连接失败', error);
        this.startReconnect();
      });
    }
  }

  /**
   * 停止重连机制
   */
  stopReconnect() {
    if (this.reconnectState.reconnectTimer) {
      clearTimeout(this.reconnectState.reconnectTimer);
      this.reconnectState.reconnectTimer = null;
    }

    this.reconnectState.isReconnecting = false;
    this.log('debug', '停止重连机制');
  }

  /**
   * 建立WebSocket连接
   * 仅在有有效token时尝试连接
   */
  async connect() {
    // 检查连接状态
    if (this.readyState === this.READY_STATE.CONNECTING ||
      this.readyState === this.READY_STATE.OPEN) {
      this.log('debug', '连接已存在或正在连接中，跳过连接请求');
      return;
    }

    // 检查token状态
    const token = wx.getStorageSync('access_token');
    if (!token) {
      const error = new Error('无access_token，无法建立WebSocket连接');
      this.log('warn', error.message);
      throw error;
    }

    // 如果正在等待token，不应该执行到这里
    if (this.tokenState.isWaitingForToken) {
      const error = new Error('正在等待token，不应建立连接');
      this.log('warn', error.message);
      throw error;
    }

    const url = this.config.server.baseUrl + token;
    this.log('info', '开始建立WebSocket连接');

    // 更新连接状态
    this.readyState = this.READY_STATE.CONNECTING;

    try {

      // 创建WebSocket连接
      this.socketTask = wx.connectSocket({
        url: url,

        timeout: this.config.server.timeout,

      });

      // 设置事件监听器
      this.setupEventListeners();

      this.log('info', 'WebSocket连接请求已发送');

    } catch (error) {

      this.log('error', 'WebSocket连接建立失败', error);
      this.readyState = this.READY_STATE.CLOSED;
      throw error;
    }
  }

  /**
   * 设置WebSocket事件监听器
   */
  setupEventListeners() {
    if (!this.socketTask) return;

    this.socketTask.onOpen(this.handleOpen);
    this.socketTask.onMessage(this.handleMessage);
    this.socketTask.onClose(this.handleClose);
    this.socketTask.onError(this.handleError);
  }

  /**
   * 处理连接打开事件
   */
  handleOpen(res) {
    this.log('info', 'WebSocket连接已建立');

    // 更新连接状态
    this.readyState = this.READY_STATE.OPEN;

    // 重置重连状态
    this.reconnectState.currentAttempts = 0;
    this.reconnectState.isReconnecting = false;
    this.stopReconnect();

    // 隐藏可能存在的重连提示
    wx.hideLoading();

    // 启动心跳机制（仅在有token时）
    const token = wx.getStorageSync('access_token');
    if (token) {
      this.startHeartbeat();
    }

    // 处理消息队列
    this.processMessageQueue();

    // 触发open事件
    this.emit('open', res);
  }

  /**
   * 处理消息接收事件
   */
  handleMessage(res) {

    this.log('debug', '收到WebSocket消息', this.logging.logMessageContent ? res : { dataLength: res.data.length });

    try {

      const data = JSON.parse(res.data);

      // 检查消息格式
      if (!data.topic) {
        this.log('warn', '收到无效消息格式，缺少topic字段', data);
        return;
      }

      // 处理不同类型的消息
      switch (data.topic) {
        case this.TOPICS.PONG:
          // 处理心跳响应
          this.log('debug', '收到心跳响应');
          this.handleHeartbeatResponse();
          break;

        case this.TOPICS.SEND_PRIVATE_MESSAGE_RESPONSE:
          // 处理私聊消息发送响应
          this.handlePrivateMessageResponse(data.message);
          break;

        case this.TOPICS.SEND_PRIVATE_MESSAGE:
        case this.TOPICS.SUBS_PRIVATE_MESSAGE:
          // 处理接收到的私聊消息（发送和订阅都用同一个处理方法）
          this.handlePrivateMessageReceived(data.message);
          break;

        case this.TOPICS.SUBS_SYS_MESSAGE:
          // 处理服务器推送的系统消息
          this.handleSystemMessageReceived(data.message);
          break;

        case this.TOPICS.SUBS_NOTICE:
          // 处理服务器推送的通知公告
          this.handleNoticeReceived(data.message);
          break;

        default:
          // 其他消息类型
          this.log('info', '收到未处理的消息类型', { topic: data.topic });
          this.emit('message', data);
      }

    } catch (error) {

      this.log('error', '消息解析失败', { error: error.message, data: res.data });
      this.emit('error', { type: 'parse_error', data: res.data, error });
    }
  }

  /**
   * 处理连接关闭事件
   */
  handleClose(res) {
    this.log('info', 'WebSocket连接已关闭', { code: res.code, reason: res.reason });

    // 更新连接状态
    this.readyState = this.READY_STATE.CLOSED;

    // 停止心跳
    this.stopHeartbeat();

    // 触发close事件
    this.emit('close', res);

    // 检查token状态决定重连策略
    const token = wx.getStorageSync('access_token');
    const tokenExpireTime = wx.getStorageSync('token_expire_time');
    const isTokenExpired = this.isTokenExpired(token, tokenExpireTime);

    if (!token) {
      this.log('info', '连接关闭且无token，进入等待模式', { code: res.code });
      this.handleNoToken();
    } else if (isTokenExpired) {
      this.log('info', '连接关闭且token过期，尝试刷新token', { code: res.code });
      this.handleTokenExpired();
    } else if (!this.tokenState.isWaitingForToken) {
      this.log('info', '连接关闭，启动重连机制保持连接', { code: res.code });
      this.startReconnect();
    } else {
      this.log('info', '等待token状态，进入等待模式', { code: res.code });
      this.handleNoToken();
    }
  }

  /**
   * 处理连接错误事件
   */
  handleError(res) {

    this.log('error', 'WebSocket连接错误', res);

    // 更新连接状态
    this.readyState = this.READY_STATE.CLOSED;

    // 停止心跳
    this.stopHeartbeat();

    // 触发error事件
    this.emit('error', res);

    // 检查token状态后决定是否重连
    const token = wx.getStorageSync('access_token');
    const tokenExpireTime = wx.getStorageSync('token_expire_time');
    const isTokenExpired = this.isTokenExpired(token, tokenExpireTime);

    if (!token) {
      this.log('info', '连接错误且无token，进入等待状态');
      this.handleNoToken();
    } else if (isTokenExpired) {
      this.log('info', '连接错误且token过期，尝试刷新token');
      this.handleTokenExpired();
    } else if (!this.tokenState.isWaitingForToken && !this.reconnectState.isReconnecting) {
      this.log('info', '连接错误且有有效token，启动重连机制');
      this.startReconnect();
    } else {
      this.log('debug', '连接错误，但已在重连或等待状态中');
    }
  }

  /**
   * 处理私聊消息发送响应
   * @param {string|number} responseId - 响应ID
   */
  handlePrivateMessageResponse(responseId) {
    this.log('info', '收到私聊消息发送响应', { responseId });

    // 检查响应ID是否大于0，表示发送成功
    const isSuccess = responseId && Number(responseId) > 0;

    if (isSuccess) {
      this.log('info', '私聊消息发送成功', { responseId });
      this.emit('messageResponse', { success: true, responseId });
    } else {
      this.log('warn', '私聊消息发送失败', { responseId });
      this.emit('messageResponse', { success: false, responseId });
    }
  }

  /**
   * 处理接收到的私聊消息
   * @param {Object} message - 消息内容
   */
  handlePrivateMessageReceived(message) {
    this.log('info', '收到私聊消息', this.logging.logMessageContent ? message : { type: message.type });

    // 触发消息接收事件
    this.emit('privateMessage', message);
  }

  /**
   * 处理服务器推送的系统消息
   * @param {Object} message - 系统消息内容
   */
  handleSystemMessageReceived(message) {
    this.log('info', '收到系统消息', this.logging.logMessageContent ? message : { id: message.id });

    // 触发系统消息接收事件
    this.emit('systemMessage', message);
  }

  /**
   * 处理服务器推送的通知公告
   * @param {Object} message - 通知公告内容
   */
  handleNoticeReceived(message) {
    this.log('info', '收到通知公告', this.logging.logMessageContent ? message : { id: message.id });

    // 触发通知公告接收事件
    this.emit('noticeMessage', message);
  }



  /**
   * 发送消息
   * @param {Object} messageData - 消息数据
   * @param {string} messageData.topic - 消息主题
   * @param {Object} messageData.message - 消息内容
   */
  sendMessage(messageData) {
    const message = {
      topic: messageData.topic,
      message: messageData.message
    };

    if (this.readyState === this.READY_STATE.OPEN) {
      try {
        this.socketTask.send({
          data: JSON.stringify(message)
        });
        this.log('info', '消息发送成功', this.logging.logMessageContent ? message : { topic: message.topic });
        return true;
      } catch (error) {
        this.log('error', '消息发送失败', error);
        // 发送失败时加入队列
        this.addToQueue(message);
        return false;
      }
    } else {
      this.log('info', '连接未就绪，消息加入队列', { topic: message.topic });
      this.addToQueue(message);

      // 如果连接已关闭，尝试重连
      if (this.readyState === this.READY_STATE.CLOSED) {
        this.startReconnect();
      }

      return false;
    }
  }

  /**
   * 发送私聊消息
   * @param {string} type - 消息类型 (text/image)
   * @param {string} content - 消息内容
   * @param {string} receiverId - 接收者ID
   * @returns {boolean} 发送是否成功
   */
  sendPrivateMessage(type, content, receiverId) {
    // 验证消息类型
    if (!this.config.message.supportedTypes.includes(type)) {
      this.log('warn', '不支持的消息类型', { type, supportedTypes: this.config.message.supportedTypes });
      return false;
    }

    // 验证必要参数
    if (!content || !receiverId) {
      this.log('warn', '消息内容或接收者ID不能为空', { content: !!content, receiverId: !!receiverId });
      return false;
    }

    return this.sendMessage({
      topic: this.TOPICS.SEND_PRIVATE_MESSAGE,
      message: {
        type: type,
        content: content,
        receiverId: receiverId
      }
    });
  }



  /**
   * 处理消息队列
   */
  processMessageQueue() {
    if (this.messageQueue.length === 0) {
      this.log('debug', '消息队列为空');
      return;
    }

    this.log('info', '处理消息队列', { queueLength: this.messageQueue.length });

    const queue = [...this.messageQueue];
    this.messageQueue.length = 0; // 清空队列

    queue.forEach((message, index) => {
      try {
        this.socketTask.send({
          data: JSON.stringify(message)
        });
        this.log('debug', `队列消息 ${index + 1}/${queue.length} 发送成功`,
          this.logging.logMessageContent ? message : { messageId: message.messageId });
      } catch (error) {
        this.log('error', `队列消息 ${index + 1}/${queue.length} 发送失败`, error);
        // 发送失败的消息重新加入队列（如果队列未满）
        if (this.messageQueue.length < this.maxQueueLength) {
          this.messageQueue.push(message);
        } else {
          this.log('warn', '消息队列已满，丢弃消息', { messageId: message.messageId });
        }
      }
    });
  }

  /**
   * 添加消息到队列
   * @param {Object} message - 消息对象
   */
  addToQueue(message) {
    if (this.messageQueue.length >= this.maxQueueLength) {
      this.log('warn', '消息队列已满，移除最旧的消息');
      this.messageQueue.shift(); // 移除最旧的消息
    }

    this.messageQueue.push(message);
    this.log('debug', '消息已加入队列', {
      topic: message.topic,
      queueLength: this.messageQueue.length
    });
  }

  /**
   * 启动心跳机制
   * 仅在连接正常且有token时启动
   */
  startHeartbeat() {
    // 检查前置条件
    if (this.readyState !== this.READY_STATE.OPEN) {
      this.log('warn', '连接未建立，无法启动心跳');
      return;
    }

    const token = wx.getStorageSync('access_token');
    if (!token) {
      this.log('warn', '无token，无法启动心跳');
      return;
    }

    // 先停止现有心跳
    this.stopHeartbeat();

    this.log('info', '启动心跳机制', { interval: this.heartbeatState.interval });

    // 标记心跳为活跃状态
    this.heartbeatState.isActive = true;

    // 启动心跳定时器
    this.heartbeatState.timer = setInterval(() => {
      // 检查连接状态和token状态
      if (this.readyState === this.READY_STATE.OPEN && wx.getStorageSync('access_token')) {
        this.sendHeartbeat();
      } else {
        this.log('warn', '心跳检查时发现连接或token异常，停止心跳');
        this.stopHeartbeat();
      }
    }, this.heartbeatState.interval);
  }

  /**
   * 停止心跳机制
   */
  stopHeartbeat() {
    if (this.heartbeatState.timer) {
      clearInterval(this.heartbeatState.timer);
      this.heartbeatState.timer = null;
    }

    if (this.heartbeatState.timeoutTimer) {
      clearTimeout(this.heartbeatState.timeoutTimer);
      this.heartbeatState.timeoutTimer = null;
    }

    // 重置心跳状态
    this.heartbeatState.missedCount = 0;
    this.heartbeatState.isActive = false;

    this.log('debug', '心跳机制已停止');
  }

  /**
   * 发送心跳包
   */
  sendHeartbeat() {
    try {
      const heartbeatMessage = {
        topic: this.TOPICS.PING,
        message: {}
      };

      this.socketTask.send({
        data: JSON.stringify(heartbeatMessage)
      });

      this.log('debug', '发送心跳包');

      // 设置心跳超时检测
      this.heartbeatState.timeoutTimer = setTimeout(() => {
        this.handleHeartbeatTimeout();
      }, this.heartbeatState.timeout);

    } catch (error) {
      this.log('error', '心跳包发送失败', error);
      this.handleHeartbeatTimeout();
    }
  }

  /**
   * 处理心跳响应
   */
  handleHeartbeatResponse() {
    this.log('debug', '收到心跳响应');

    // 清除超时定时器
    if (this.heartbeatState.timeoutTimer) {
      clearTimeout(this.heartbeatState.timeoutTimer);
      this.heartbeatState.timeoutTimer = null;
    }

    // 重置丢失计数
    this.heartbeatState.missedCount = 0;
  }

  /**
   * 处理心跳超时
   */
  handleHeartbeatTimeout() {
    this.heartbeatState.missedCount++;
    this.log('warn', '心跳超时', {
      missedCount: this.heartbeatState.missedCount,
      maxMissed: this.heartbeatState.maxMissed
    });

    if (this.heartbeatState.missedCount >= this.heartbeatState.maxMissed) {
      this.log('error', '心跳连续丢失过多，认为连接已断开');

      // 停止心跳
      this.stopHeartbeat();

      // 关闭连接，这会触发 handleClose 方法，自动启动重连
      this.close(1006, 'Heartbeat timeout');
    }
  }

  /**
   * 启动重连机制
   * 仅在有token且未达到最大重连次数时启动
   */
  startReconnect() {
    // 检查是否已在重连中
    if (this.reconnectState.isReconnecting) {
      this.log('debug', '重连已在进行中，跳过重复请求');
      return;
    }

    // 检查token状态
    const token = wx.getStorageSync('access_token');
    if (!token) {
      this.log('info', '无token，不启动重连，进入等待状态');
      this.handleNoToken();
      return;
    }

    // 检查是否在等待token状态
    if (this.tokenState.isWaitingForToken) {
      this.log('info', '正在等待token，不启动重连');
      return;
    }

    // 检查重连次数（改为警告而不是停止）
    if (this.reconnectState.currentAttempts >= this.reconnectState.maxAttempts) {
      this.log('warn', `重连次数已达到${this.reconnectState.maxAttempts}次，但继续尝试重连`);
      // 重置计数器，避免无限增长
      this.reconnectState.currentAttempts = 0;
    }

    // 开始重连
    this.reconnectState.isReconnecting = true;
    this.reconnectState.currentAttempts++;

    // 计算延迟时间
    let delay;
    if (this.reconnectState.useExponentialBackoff) {
      // 指数退避算法
      delay = Math.min(
        this.reconnectState.baseDelay * Math.pow(2, this.reconnectState.currentAttempts - 1),
        this.reconnectState.maxDelay
      );
    } else {
      // 固定延迟
      delay = this.reconnectState.baseDelay;
    }

    this.log('info', `开始第 ${this.reconnectState.currentAttempts} 次重连`, {
      delay,
      useExponentialBackoff: this.reconnectState.useExponentialBackoff
    });

    // 显示重连提示
    // wx.showLoading({
    //   title: `重连中 (${this.reconnectState.currentAttempts}/${this.reconnectState.maxAttempts})`,
    //   mask: false
    // });

    // 设置重连定时器
    this.reconnectState.reconnectTimer = setTimeout(async () => {
      try {
        // 重连前再次检查token状态
        const currentToken = wx.getStorageSync('access_token');
        if (!currentToken) {
          wx.hideLoading();
          this.log('warn', '重连时发现token已失效');
          this.reconnectState.isReconnecting = false;
          this.handleNoToken();
          return;
        }

        // 尝试连接
        await this.connect();

        // 连接成功，隐藏加载提示
        wx.hideLoading();
        this.log('info', '重连成功');

        // 重置重连状态
        this.reconnectState.isReconnecting = false;

        this.emit('reconnect', {
          success: true,
          attempts: this.reconnectState.currentAttempts
        });

      } catch (error) {
        wx.hideLoading();
        this.log('error', `第 ${this.reconnectState.currentAttempts} 次重连失败`, error);

        // 重置重连状态，准备下次重连
        this.reconnectState.isReconnecting = false;

        // 检查是否还有重连机会
        if (this.reconnectState.currentAttempts < this.reconnectState.maxAttempts) {
          // 继续尝试重连
          setTimeout(() => {
            this.startReconnect();
          }, 1000); // 短暂延迟后重试
        } else {
          // 达到最大重连次数
          this.log('error', '达到最大重连次数，停止重连');
          this.emit('reconnect', {
            success: false,
            attempts: this.reconnectState.currentAttempts,
            reason: 'max_attempts_reached'
          });
        }
      }
    }, delay);
  }

  /**
   * 关闭WebSocket连接
   * @param {number} code - 关闭代码
   * @param {string} reason - 关闭原因
   */
  close(code = 1000, reason = 'Normal closure') {
    this.log('info', '主动关闭连接', { code, reason });

    this.stopHeartbeat();
    this.reconnectState.isReconnecting = false;

    if (this.socketTask && this.readyState !== this.READY_STATE.CLOSED) {
      this.readyState = this.READY_STATE.CLOSING;

      this.socketTask.close({
        code: code,
        reason: reason
      });
    }

    this.readyState = this.READY_STATE.CLOSED;
  }

  /**
   * 销毁WebSocket管理器
   * 清理所有定时器和状态
   */
  destroy() {
    this.log('info', '销毁WebSocket管理器');

    // 关闭连接
    this.close(1000, 'Manager destroyed');

    // 停止所有定时器
    this.stopHeartbeat();
    this.stopReconnect();

    // 停止token监控
    if (this.tokenState.checkTimer) {
      clearInterval(this.tokenState.checkTimer);
      this.tokenState.checkTimer = null;
    }

    // 清空消息队列
    this.messageQueue.length = 0;

    // 清空事件监听器
    this.eventListeners = {
      open: [],
      message: [],
      close: [],
      error: [],
      reconnect: [],
      tokenChange: [],
      messageResponse: [],
      privateMessage: []
    };

    // 重置状态
    this.readyState = this.READY_STATE.CLOSED;
    this.isInitialized = false;
    this.tokenState.isWaitingForToken = false;
    this.reconnectState.isReconnecting = false;
    this.heartbeatState.isActive = false;

    this.log('info', 'WebSocket管理器已销毁');
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  on(event, listener) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].push(listener);
    }
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  off(event, listener) {
    if (this.eventListeners[event]) {
      const index = this.eventListeners[event].indexOf(listener);
      if (index > -1) {
        this.eventListeners[event].splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {*} data - 事件数据
   */
  emit(event, data) {
    if (this.eventListeners[event]) {
      this.eventListeners[event].forEach(listener => {
        try {
          listener(data);
        } catch (error) {
          console.error(`WebSocketManager: 事件监听器执行错误 [${event}]`, error);
        }
      });
    }
  }



  /**
   * 获取连接状态
   */
  getReadyState() {
    return this.readyState;
  }

  /**
   * 检查连接是否正常
   */
  isConnected() {
    return this.readyState === this.READY_STATE.OPEN;
  }

  /**
   * 获取连接统计信息
   */
  getStats() {
    const token = wx.getStorageSync('access_token');

    return {
      // 连接状态
      readyState: this.readyState,
      isConnected: this.readyState === this.READY_STATE.OPEN,
      isInitialized: this.isInitialized,

      // Token状态
      hasToken: !!token,
      isWaitingForToken: this.tokenState.isWaitingForToken,

      // 重连状态
      reconnectAttempts: this.reconnectState.currentAttempts,
      maxReconnectAttempts: this.reconnectState.maxAttempts,
      isReconnecting: this.reconnectState.isReconnecting,

      // 心跳状态
      heartbeatActive: this.heartbeatState.isActive,
      heartbeatMissedCount: this.heartbeatState.missedCount,
      maxHeartbeatMissed: this.heartbeatState.maxMissed,

      // 消息队列
      messageQueueLength: this.messageQueue.length,
      maxQueueLength: this.maxQueueLength,

      // 配置信息
      serverUrl: this.config.server.baseUrl,
      heartbeatInterval: this.heartbeatState.interval,
      tokenCheckInterval: this.tokenState.checkInterval
    };
  }

  /**
   * 重置重连计数器
   */
  resetReconnectCounter() {
    this.reconnectState.currentAttempts = 0;
    this.reconnectState.isReconnecting = false;
    this.stopReconnect();
    this.log('info', '重连计数器已重置');
  }

  /**
   * 获取当前token状态
   */
  getTokenStatus() {
    const token = wx.getStorageSync('access_token');
    return {
      hasToken: !!token,
      isWaitingForToken: this.tokenState.isWaitingForToken,
      lastToken: this.tokenState.lastToken
    };
  }

  /**
   * 手动触发token状态检查
   */
  forceTokenCheck() {
    this.log('info', '手动触发token状态检查');
    this.checkTokenStatus();
  }
}

// 创建单例实例
const websocketManager = new WebSocketManager();

module.exports = websocketManager;
