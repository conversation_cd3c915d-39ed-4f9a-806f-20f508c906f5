// 私信消息处理测试页面
Page({
  data: {
    testMessage: {
      content: "测试消息内容",
      createTime: "2025-07-01 15:45:16",
      id: "214",
      receiverId: "32",
      senderId: "33",
      status: "UNREAD",
      type: "text"
    }
  },

  onLoad: function (options) {
    console.log('消息测试页面加载');
  },

  // 模拟收到新私信
  simulateNewMessage: function() {
    const testMessage = {
      content: "这是一条测试私信消息",
      createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      id: Date.now().toString(),
      receiverId: wx.getStorageSync('userInfo')?.id || "32",
      senderId: "test_user_" + Math.floor(Math.random() * 1000),
      status: "UNREAD",
      type: "text",
      senderName: "测试用户" + Math.floor(Math.random() * 100),
      senderAvatar: ""
    };

    console.log('模拟发送私信消息:', testMessage);

    // 获取当前页面栈
    const pages = getCurrentPages();
    
    // 查找messages页面
    const messagesPage = pages.find(page => 
      page.route === 'pages/messages/messages' || 
      page.route.endsWith('/messages/messages')
    );

    if (messagesPage && messagesPage.handlePrivateMessage) {
      messagesPage.handlePrivateMessage(testMessage);
      wx.showToast({
        title: '已发送测试消息',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '未找到消息页面',
        icon: 'none'
      });
    }
  },

  // 模拟收到已存在用户的私信
  simulateExistingUserMessage: function() {
    const testMessage = {
      content: "来自已存在用户的消息",
      createTime: new Date().toISOString().replace('T', ' ').substring(0, 19),
      id: Date.now().toString(),
      receiverId: wx.getStorageSync('userInfo')?.id || "32",
      senderId: "33", // 使用固定的已存在用户ID
      status: "UNREAD",
      type: "text",
      senderName: "已存在用户",
      senderAvatar: ""
    };

    console.log('模拟已存在用户发送私信:', testMessage);

    // 获取当前页面栈
    const pages = getCurrentPages();
    
    // 查找messages页面
    const messagesPage = pages.find(page => 
      page.route === 'pages/messages/messages' || 
      page.route.endsWith('/messages/messages')
    );

    if (messagesPage && messagesPage.handlePrivateMessage) {
      messagesPage.handlePrivateMessage(testMessage);
      wx.showToast({
        title: '已发送测试消息',
        icon: 'success'
      });
    } else {
      wx.showToast({
        title: '未找到消息页面',
        icon: 'none'
      });
    }
  },

  // 跳转到消息页面
  goToMessages: function() {
    wx.navigateTo({
      url: '/pages/messages/messages'
    });
  }
});
