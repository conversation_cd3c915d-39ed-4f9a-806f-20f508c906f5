<!--私信消息处理测试页面-->
<view class="container">
  <view class="header">
    <text class="title">私信消息处理测试</text>
  </view>

  <view class="test-section">
    <view class="section-title">测试场景</view>
    
    <view class="test-item">
      <view class="test-desc">模拟收到新用户的私信消息（将刷新私信列表）</view>
      <button class="test-btn" bindtap="simulateNewMessage">发送新用户消息</button>
    </view>

    <view class="test-item">
      <view class="test-desc">模拟收到已存在用户的私信消息（将更新现有对话）</view>
      <button class="test-btn" bindtap="simulateExistingUserMessage">发送已存在用户消息</button>
    </view>

    <view class="test-item">
      <view class="test-desc">跳转到消息页面查看效果</view>
      <button class="test-btn primary" bindtap="goToMessages">打开消息页面</button>
    </view>
  </view>

  <view class="info-section">
    <view class="section-title">测试说明</view>
    <view class="info-text">
      1. 点击"发送新用户消息"会模拟收到一个新用户的私信，系统将刷新私信列表以获取完整信息
    </view>
    <view class="info-text">
      2. 点击"发送已存在用户消息"会模拟收到已存在用户的私信，应该更新现有对话并移到顶部
    </view>
    <view class="info-text">
      3. 测试前请先打开消息页面，然后返回此页面进行测试
    </view>
  </view>

  <view class="current-message">
    <view class="section-title">当前测试消息格式</view>
    <view class="message-preview">
      <text class="json-text">{{JSON.stringify(testMessage, null, 2)}}</text>
    </view>
  </view>
</view>
