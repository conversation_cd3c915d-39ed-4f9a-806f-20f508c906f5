// pages/property/staff/staff-stats.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    isLoading: true,
    
    // 统计数据
    totalStaff: 0,
    activeStaff: 0,
    inactiveStaff: 0,
    
    // 部门分布
    departmentData: [],
    
    // 性别比例
    genderData: [],
    
    // 年龄分布
    ageData: []
  },

  onLoad: function() {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '员工统计'
    });
    
    // 加载统计数据
    this.loadStatsData();
  },
  
  // 加载统计数据
  loadStatsData: function() {
    this.setData({
      isLoading: true
    });

    // 获取所有员工数据进行统计
    const propertyApi = require('@/api/propertyApi.js');
    const params = {
      pageNum: 1,
      pageSize: 500, // 获取所有员工
      personName: '',
      phone: '',
      personNumber: ''
    };

    console.log('加载员工统计数据，参数:', params);

    propertyApi.getPersonList(params).then(res => {
      console.log('员工统计API响应:', res);

      if (res && res.list && Array.isArray(res.list)) {
        const staffList = res.list;

        // 计算统计数据
        this.calculateStats(staffList);
      } else {
        console.log('员工数据为空');
        this.setData({
          isLoading: false,
          totalStaff: 0,
          activeStaff: 0,
          inactiveStaff: 0,
          departmentData: [],
          genderData: [],
          ageData: []
        });
      }
    }).catch(err => {
      console.error('获取员工统计数据失败:', err);
      this.setData({
        isLoading: false
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  // 计算统计数据
  calculateStats: function(staffList) {
    console.log('开始计算统计数据，员工总数:', staffList.length);

    // 获取字典数据
    const personStatusDict = util.getDictByNameEn('person_status');
    const genderDict = util.getDictByNameEn('gender');

    const statusOptions = personStatusDict && personStatusDict.length > 0 ? personStatusDict[0].children : [];
    const genderOptions = genderDict && genderDict.length > 0 ? genderDict[0].children : [];

    // 获取组织数据
    this.loadOrgData().then(orgList => {
      // 基础统计
      const totalStaff = staffList.length;
      let activeStaff = 0;
      let inactiveStaff = 0;

      // 部门统计
      const departmentStats = {};
      // 性别统计
      const genderStats = {};
      // 年龄统计
      const ageStats = {
        '20岁以下': 0,
        '20-30岁': 0,
        '30-40岁': 0,
        '40-50岁': 0,
        '50岁以上': 0
      };

      // 遍历员工数据进行统计
      staffList.forEach(staff => {
        // 状态统计
        if (staff.status === 'active') {
          activeStaff++;
        } else {
          inactiveStaff++;
        }

        // 部门统计
        const deptName = this.getOrgName(staff.orgId, orgList);
        if (deptName) {
          departmentStats[deptName] = (departmentStats[deptName] || 0) + 1;
        }

        // 性别统计
        const genderName = this.getGenderName(staff.gender, genderOptions);
        if (genderName) {
          genderStats[genderName] = (genderStats[genderName] || 0) + 1;
        }

        // 年龄统计
        const age = staff.age || 0;
        if (age < 20) {
          ageStats['20岁以下']++;
        } else if (age <= 30) {
          ageStats['20-30岁']++;
        } else if (age <= 40) {
          ageStats['30-40岁']++;
        } else if (age <= 50) {
          ageStats['40-50岁']++;
        } else {
          ageStats['50岁以上']++;
        }
      });

      // 转换为图表数据格式
      const departmentData = Object.keys(departmentStats).map(name => ({
        name: name,
        value: departmentStats[name],
        percentage: ((departmentStats[name] / totalStaff) * 100).toFixed(1) + '%'
      }));

      const genderData = Object.keys(genderStats).map(name => ({
        name: name,
        value: genderStats[name],
        percentage: ((genderStats[name] / totalStaff) * 100).toFixed(1) + '%'
      }));

      const ageData = Object.keys(ageStats).map(name => ({
        name: name,
        value: ageStats[name],
        percentage: ((ageStats[name] / totalStaff) * 100).toFixed(1) + '%'
      })).filter(item => item.value > 0); // 过滤掉数量为0的年龄段

      console.log('统计计算完成:', {
        totalStaff,
        activeStaff,
        inactiveStaff,
        departmentData,
        genderData,
        ageData
      });

      this.setData({
        totalStaff: totalStaff,
        activeStaff: activeStaff,
        inactiveStaff: inactiveStaff,
        departmentData: departmentData,
        genderData: genderData,
        ageData: ageData,
        isLoading: false
      });
    });
  },

  // 获取组织数据
  loadOrgData: function() {
    return new Promise((resolve) => {
      const propertyApi = require('@/api/propertyApi.js');
      propertyApi.getOrgTree().then(res => {
        if (res && res.list && Array.isArray(res.list)) {
          resolve(res.list);
        } else {
          resolve([]);
        }
      }).catch(err => {
        console.error('获取组织数据失败:', err);
        resolve([]);
      });
    });
  },

  // 根据组织ID获取组织名称
  getOrgName: function(orgId, orgList) {
    if (!orgId || !orgList.length) return '未知部门';

    const findOrg = (orgs) => {
      for (let org of orgs) {
        if (org.id === orgId) {
          return org.orgName;
        }
        if (org.children && org.children.length > 0) {
          const found = findOrg(org.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findOrg(orgList) || '未知部门';
  },

  // 根据性别码获取性别名称
  getGenderName: function(genderCode, genderOptions) {
    if (!genderCode || !genderOptions.length) return '未知';

    const gender = genderOptions.find(g => g.nameEn === genderCode);
    return gender ? gender.nameCn : '未知';
  },
  
  // 返回员工列表
  goBack: function() {
    wx.navigateBack();
  },
  
  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadStatsData();
    wx.stopPullDownRefresh();
  }
})
