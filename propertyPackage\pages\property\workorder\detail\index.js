// pages/property/workorder/detail/index.js
const workOrderManager = require('@/utils/workorder-manager');

Page({
  data: {
    workOrder: null,
    loading: true,
    showCancelConfirm: false,
    showAssignModal: false,
    showCompleteModal: false,
    showAddRecordModal: false,
    statusBarHeight: 20, // 默认状态栏高度

    // 处理工单相关数据
    processingRemark: '',

    // 分配工单相关数据
    staffList: [],
    selectedStaffId: '',
    assignRemark: '',

    // 完成工单相关数据
    completeResult: '',
    completeImages: [],
    completeRemark: '',

    // 添加处理记录相关数据
    recordAction: '',
    recordRemark: ''
  },

  onLoad(options) {
    const { id } = options;

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;

    // 设置导航栏样式
    this.setData({
      statusBarHeight: statusBarHeight
    });

    if (id) {
      this.loadWorkOrderDetail(id);
      this.loadStaffList(); // 加载员工列表，用于分配工单
    } else {
      wx.showToast({
        title: '工单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载工单详情
  loadWorkOrderDetail(id) {
    this.setData({ loading: true });

    // 调用工单管理器获取工单详情
    workOrderManager.getWorkOrderDetail(id)
      .then(order => {
        this.setData({
          workOrder: order,
          loading: false
        });
      })
      .catch(error => {
        console.error('加载工单详情失败', error);
        this.setData({ loading: false });
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载员工列表
  loadStaffList() {
    // 这里应该调用API获取员工列表
    // 暂时使用模拟数据
    const mockStaffList = [
      { id: 'staff001', name: '张工', department: '工程部', position: '维修工程师' },
      { id: 'staff002', name: '李工', department: '工程部', position: '维修工程师' },
      { id: 'staff003', name: '王工', department: '工程部', position: '维修主管' }
    ];

  
    this.setData({ staffList: mockStaffList });
  },

  // 显示取消确认对话框
  showCancelConfirm() {
    this.setData({ showCancelConfirm: true });
  },

  // 隐藏取消确认对话框
  hideCancelConfirm() {
    this.setData({ showCancelConfirm: false });
  },

  // 取消工单
  cancelOrder() {
    const { workOrder } = this.data;

    // 调用工单管理器取消工单
    workOrderManager.cancelOrder(workOrder.id)
      .then(() => {
        this.hideCancelConfirm();

        // 重新加载工单详情
        this.loadWorkOrderDetail(workOrder.id);

        wx.showToast({
          title: '工单已取消',
          icon: 'success'
        });
      })
      .catch(error => {
        console.error('取消工单失败', error);
        this.hideCancelConfirm();

        wx.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        });
      });
  },

  // 显示处理工单对话框
  showProcessModal() {
    this.setData({
      processingRemark: '',
      showProcessModal: true
    });
  },

  // 隐藏处理工单对话框
  hideProcessModal() {
    this.setData({ showProcessModal: false });
  },

  // 导航到工单处理页面
  navigateToProcessPage(e) {
    const { workOrder } = this.data;
    const type = e.currentTarget.dataset.type;

    wx.navigateTo({
      url: `/propertyPackage/pages/property/workorder/process/index?id=${workOrder.id}&type=${type}`
    });
  },

  // 输入处理备注
  inputProcessingRemark(e) {
    this.setData({ processingRemark: e.detail.value });
  },

  // 处理工单
  processOrder() {
    const { workOrder, processingRemark } = this.data;

    wx.showLoading({ title: '处理中...' });

    // 调用工单管理器处理工单
    workOrderManager.processOrder(workOrder.id, processingRemark)
      .then(updatedOrder => {
        wx.hideLoading();

        this.setData({
          workOrder: updatedOrder,
          showProcessModal: false
        });

        wx.showToast({
          title: '工单已开始处理',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('处理工单失败', error);

        wx.showToast({
          title: error.message || '处理失败，请重试',
          icon: 'none'
        });
      });
  },

  // 显示分配工单对话框
  showAssignModal() {
    this.setData({
      selectedStaffId: '',
      assignRemark: '',
      showAssignModal: true
    });
  },

  // 隐藏分配工单对话框
  hideAssignModal() {
    this.setData({ showAssignModal: false });
  },

  // 选择员工
  selectStaff(e) {
    this.setData({ selectedStaffId: e.currentTarget.dataset.id });
  },

  // 输入分配备注
  inputAssignRemark(e) {
    this.setData({ assignRemark: e.detail.value });
  },

  // 分配工单
  assignOrder() {
    const { workOrder, selectedStaffId, assignRemark, staffList } = this.data;

    if (!selectedStaffId) {
      wx.showToast({
        title: '请选择员工',
        icon: 'none'
      });
      return;
    }

    // 获取选中的员工信息
    const selectedStaff = staffList.find(staff => staff.id === selectedStaffId);

    wx.showLoading({ title: '处理中...' });

    // 调用工单管理器分配工单
    workOrderManager.assignOrder(workOrder.id, selectedStaff, assignRemark)
      .then(updatedOrder => {
        wx.hideLoading();

        this.setData({
          workOrder: updatedOrder,
          showAssignModal: false
        });

        wx.showToast({
          title: '工单已分配',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('分配工单失败', error);

        wx.showToast({
          title: error.message || '分配失败，请重试',
          icon: 'none'
        });
      });
  },

  // 显示完成工单对话框
  showCompleteModal() {
    this.setData({
      completeResult: '',
      completeImages: [],
      completeRemark: '',
      showCompleteModal: true
    });
  },

  // 隐藏完成工单对话框
  hideCompleteModal() {
    this.setData({ showCompleteModal: false });
  },

  // 输入完成结果
  inputCompleteResult(e) {
    this.setData({ completeResult: e.detail.value });
  },

  // 输入完成备注
  inputCompleteRemark(e) {
    this.setData({ completeRemark: e.detail.value });
  },

  // 上传完成图片
  uploadCompleteImages() {
    wx.chooseImage({
      count: 9 - this.data.completeImages.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 更新图片列表
        this.setData({
          completeImages: [...this.data.completeImages, ...res.tempFilePaths]
        });
      }
    });
  },

  // 删除完成图片
  removeCompleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.completeImages];
    images.splice(index, 1);
    this.setData({ completeImages: images });
  },

  // 完成工单
  completeOrder() {
    const { workOrder, completeResult, completeImages, completeRemark } = this.data;

    if (!completeResult) {
      wx.showToast({
        title: '请输入处理结果',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '处理中...' });

    // 调用工单管理器完成工单
    workOrderManager.completeOrder(workOrder.id, completeResult, completeImages, completeRemark)
      .then(updatedOrder => {
        wx.hideLoading();

        this.setData({
          workOrder: updatedOrder,
          showCompleteModal: false
        });

        wx.showToast({
          title: '工单已完成',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('完成工单失败', error);

        wx.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        });
      });
  },

  // 显示添加处理记录对话框
  showAddRecordModal() {
    this.setData({
      recordAction: '',
      recordRemark: '',
      showAddRecordModal: true
    });
  },

  // 隐藏添加处理记录对话框
  hideAddRecordModal() {
    this.setData({ showAddRecordModal: false });
  },

  // 输入处理记录动作
  inputRecordAction(e) {
    this.setData({ recordAction: e.detail.value });
  },

  // 输入处理记录备注
  inputRecordRemark(e) {
    this.setData({ recordRemark: e.detail.value });
  },

  // 添加处理记录
  addProcessingRecord() {
    const { workOrder, recordAction, recordRemark } = this.data;

    if (!recordAction) {
      wx.showToast({
        title: '请输入处理动作',
        icon: 'none'
      });
      return;
    }

    if (!recordRemark) {
      wx.showToast({
        title: '请输入处理备注',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '处理中...' });

    // 调用工单管理器添加处理记录
    workOrderManager.addProcessingRecord(workOrder.id, recordAction, recordRemark)
      .then(updatedOrder => {
        wx.hideLoading();

        this.setData({
          workOrder: updatedOrder,
          showAddRecordModal: false
        });

        wx.showToast({
          title: '处理记录已添加',
          icon: 'success'
        });
      })
      .catch(error => {
        wx.hideLoading();
        console.error('添加处理记录失败', error);

        wx.showToast({
          title: error.message || '操作失败，请重试',
          icon: 'none'
        });
      });
  },

  // 预览图片
  previewImage(e) {
    const { index } = e.currentTarget.dataset;
    const { workOrder } = this.data;

    wx.previewImage({
      current: workOrder.content.images[index],
      urls: workOrder.content.images
    });
  },

  // 预览评价图片
  previewEvaluationImage(e) {
    const { index } = e.currentTarget.dataset;
    const { workOrder } = this.data;

    wx.previewImage({
      current: workOrder.evaluation.images[index],
      urls: workOrder.evaluation.images
    });
  },

  // 导航返回
  navigateBack() {
    wx.navigateBack();
  }
});
