// pages/property/staff/staff-detail.js
const util = require('../../../../utils/util.js')

Page({
  data: {
    darkMode: false,
    staffId: '',
    staffInfo: null,
    isLoading: true,
    showActionSheet: false,
    actions: [
      { name: '编辑信息', color: '#007aff', icon: 'edit' },
      { name: '删除员工', color: '#ff3b30', icon: 'delete' }
    ]
  },

  onLoad: function(options) {
    // 检查暗黑模式
    const app = getApp()
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      })
    }

    // 获取员工ID
    if (options.id) {
      this.setData({
        staffId: options.id
      });

      // 加载员工详情
      this.loadStaffDetail();
    } else {
      wx.showToast({
        title: '员工ID不存在',
        icon: 'none',
        duration: 2000,
        complete: () => {
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      });
    }
  },

  // 加载员工详情
  loadStaffDetail: function() {
    this.setData({
      isLoading: true
    });

    const propertyApi = require('@/api/propertyApi.js');

    console.log('加载员工详情，ID:', this.data.staffId);

    propertyApi.personInfo(this.data.staffId).then(res => {
      console.log('员工详情API响应:', res);

      if (res) {
        // 格式化员工详情数据
        this.formatStaffDetail(res);
      } else {
        wx.showToast({
          title: '员工信息不存在',
          icon: 'none'
        });
        setTimeout(() => {
          wx.navigateBack();
        }, 2000);
      }
    }).catch(err => {
      console.error('获取员工详情失败:', err);
      this.setData({
        isLoading: false
      });

      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  // 格式化员工详情数据
  formatStaffDetail: function(rawData) {
    console.log('格式化员工详情数据:', rawData);

    // 初始化字典数据和组织数据
    Promise.all([
      this.loadDictData(),
      this.loadOrgData(),
      this.loadPositionData()
    ]).then(() => {
      // 获取组织名称
      const orgName = this.getOrgName(rawData.orgId);

      // 获取职位名称
      const positionName = this.getPositionName(rawData.positionId);

      // 获取状态显示文本
      const statusText = this.getStatusText(rawData.status);

      // 获取性别显示文本
      const genderText = this.getGenderText(rawData.gender);

      // 处理证件照片路径（media字段是证件照片，不用作头像）
      const idCardPhoto = rawData.media ?
        `${wx.getStorageSync('apiUrl')}/common-api/v1/file/${rawData.media}` :
        '';

      const staffInfo = {
        id: rawData.id,
        name: rawData.personName || '',
        gender: genderText,
        age: rawData.age || 0,
        phone: rawData.phone || '',
        idCard: rawData.idCard || '',
        employeeId: rawData.personNumber || '',
        organization: orgName,
        position: positionName,
        entryDate: rawData.entryTime ? rawData.entryTime.split(' ')[0] : '',
        idCardPhoto: idCardPhoto, // 证件照片
        status: statusText,
        email: rawData.email || '',
        address: rawData.address || '',
        salary: rawData.salary || 0,
        major: rawData.major || '',
        skills: rawData.skills || '',
        performance: rawData.performance || '',
        notes: rawData.note || '',
        certificates: rawData.certificates || '',
        // 原始数据保留用于编辑
        rawData: rawData
      };

      this.setData({
        staffInfo: staffInfo,
        isLoading: false
      });

      console.log('员工详情格式化完成:', staffInfo);
    });
  },

  // 加载字典数据
  loadDictData: function() {
    return new Promise((resolve) => {
      const personStatusDict = util.getDictByNameEn('person_status');
      const genderDict = util.getDictByNameEn('gender');

      this.setData({
        personStatusDict: personStatusDict && personStatusDict.length > 0 ? personStatusDict[0].children : [],
        genderDict: genderDict && genderDict.length > 0 ? genderDict[0].children : []
      });

      resolve();
    });
  },

  // 加载组织数据
  loadOrgData: function() {
    return new Promise((resolve) => {
      const propertyApi = require('@/api/propertyApi.js');
      propertyApi.getOrgTree().then(res => {
        if (res && res.list && Array.isArray(res.list)) {
          this.setData({
            orgList: res.list
          });
        }
        resolve();
      }).catch(err => {
        console.error('获取组织数据失败:', err);
        resolve();
      });
    });
  },

  // 加载职位数据
  loadPositionData: function() {
    return new Promise((resolve) => {
      const commApi = require('@/api/commApi.js');
      const params = { pageNum: 1, pageSize: 100 };

      commApi.getPositionPage(params).then(res => {
        if (res && res.list && Array.isArray(res.list)) {
          this.setData({
            positionList: res.list
          });
        }
        resolve();
      }).catch(err => {
        console.error('获取职位数据失败:', err);
        resolve();
      });
    });
  },

  // 根据组织ID获取组织名称
  getOrgName: function(orgId) {
    if (!orgId || !this.data.orgList || !this.data.orgList.length) return '未知组织';

    const findOrg = (orgs) => {
      for (let org of orgs) {
        if (org.id === orgId) {
          return org.orgName;
        }
        if (org.children && org.children.length > 0) {
          const found = findOrg(org.children);
          if (found) return found;
        }
      }
      return null;
    };

    return findOrg(this.data.orgList) || '未知组织';
  },

  // 根据职位ID获取职位名称
  getPositionName: function(positionId) {
    if (!positionId || !this.data.positionList || !this.data.positionList.length) return '未知职位';

    const position = this.data.positionList.find(p => p.id === positionId);
    return position ? position.positionName : '未知职位';
  },

  // 根据状态码获取状态文本
  getStatusText: function(statusCode) {
    if (!statusCode || !this.data.personStatusDict || !this.data.personStatusDict.length) return '未知状态';

    const status = this.data.personStatusDict.find(s => s.nameEn === statusCode);
    return status ? status.nameCn : '未知状态';
  },

  // 根据性别码获取性别文本
  getGenderText: function(genderCode) {
    if (!genderCode || !this.data.genderDict || !this.data.genderDict.length) return '未知';

    const gender = this.data.genderDict.find(g => g.nameEn === genderCode);
    return gender ? gender.nameCn : '未知';
  },

  // 显示操作菜单
  showActions: function() {
    this.setData({
      showActionSheet: true
    });
  },

  // 隐藏操作菜单
  hideActions: function() {
    this.setData({
      showActionSheet: false
    });
  },

  // 处理操作菜单选择
  handleActionClick: function(e) {
    const index = e.currentTarget.dataset.index;
    this.hideActions();

    switch(index) {
      case 0: // 编辑信息
        this.editStaff();
        break;
      case 1: // 删除员工
        this.confirmDeleteStaff();
        break;
    }
  },

  // 编辑员工信息
  editStaff: function() {
    if (this.data.staffInfo && this.data.staffInfo.rawData) {
      // 将原始数据存储到全局，供编辑页面使用
      wx.setStorageSync('editStaffData', this.data.staffInfo.rawData);

      wx.navigateTo({
        url: `./staff-edit?id=${this.data.staffId}&mode=edit`
      });
    } else {
      wx.showToast({
        title: '员工数据不完整',
        icon: 'none'
      });
    }
  },

  // 确认删除员工
  confirmDeleteStaff: function() {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除员工"${this.data.staffInfo.name}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#ff3b30',
      success: (res) => {
        if (res.confirm) {
          this.deleteStaff();
        }
      }
    });
  },

  // 删除员工
  deleteStaff: function() {
    wx.showLoading({
      title: '删除中...',
    });

    // 模拟删除操作
    setTimeout(() => {
      wx.hideLoading();

      wx.showToast({
        title: '删除成功',
        icon: 'success',
        duration: 2000,
        complete: () => {
          // 设置需要刷新员工列表的标志
          wx.setStorageSync('staffDataNeedRefresh', true);

          // 返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        }
      });
    }, 1500);
  },

  // 拨打电话
  callPhone: function() {
    if (this.data.staffInfo && this.data.staffInfo.phone) {
      wx.makePhoneCall({
        phoneNumber: this.data.staffInfo.phone
      });
    }
  },

  // 发送短信
  sendMessage: function() {
    if (this.data.staffInfo && this.data.staffInfo.phone) {
      wx.showToast({
        title: '暂不支持发送短信',
        icon: 'none'
      });
    }
  },

  // 查看大图
  viewImage: function(e) {
    const url = e.currentTarget.dataset.url;
    if (url) {
      wx.previewImage({
        urls: [url],
        current: url
      });
    }
  },

  // 页面显示时刷新数据
  onShow: function() {
    // 检查是否需要刷新数据
    const needRefresh = wx.getStorageSync('staffDetailNeedRefresh');
    if (needRefresh) {
      this.loadStaffDetail();
      wx.removeStorageSync('staffDetailNeedRefresh');
    }
  },

  // 查看大地图
  viewLargeMap: function() {
    const { staffInfo } = this.data;
    if (staffInfo && staffInfo.locationLatitude && staffInfo.locationLongitude) {
      wx.openLocation({
        latitude: staffInfo.locationLatitude,
        longitude: staffInfo.locationLongitude,
        name: staffInfo.name + '的位置',
        address: staffInfo.currentLocation,
        scale: 18
      });
    }
  },

  // 启动健康数据模拟
  startHealthDataSimulation: function() {
    // 清除之前的定时器
    if (this.healthDataTimer) {
      clearInterval(this.healthDataTimer);
    }

    // 每30秒更新一次健康数据
    this.healthDataTimer = setInterval(() => {
      if (!this.data.staffInfo) return;

      // 模拟心率变化 (正常范围60-100)
      const heartRateChange = Math.floor(Math.random() * 7) - 3; // -3到3的随机变化
      let heartRate = this.data.staffInfo.heartRate + heartRateChange;
      heartRate = Math.max(58, Math.min(102, heartRate)); // 限制在58-102范围内

      // 模拟体温变化 (正常范围36-37.2)
      const tempChange = (Math.random() * 0.4 - 0.2).toFixed(1); // -0.2到0.2的随机变化
      let temperature = parseFloat((parseFloat(this.data.staffInfo.temperature) + parseFloat(tempChange)).toFixed(1));
      temperature = Math.max(36.0, Math.min(37.5, temperature)); // 限制在36-37.5范围内

      // 模拟步数增加
      const stepsIncrease = Math.floor(Math.random() * 50); // 0到50的随机增加
      const steps = this.data.staffInfo.steps + stepsIncrease;

      // 更新健康数据
      const now = new Date();
      const healthUpdateTime = now.getFullYear() + '-' +
                              String(now.getMonth() + 1).padStart(2, '0') + '-' +
                              String(now.getDate()).padStart(2, '0') + ' ' +
                              String(now.getHours()).padStart(2, '0') + ':' +
                              String(now.getMinutes()).padStart(2, '0') + ':' +
                              String(now.getSeconds()).padStart(2, '0');

      this.setData({
        'staffInfo.heartRate': heartRate,
        'staffInfo.temperature': temperature,
        'staffInfo.steps': steps,
        'staffInfo.healthUpdateTime': healthUpdateTime
      });
    }, 30000); // 30秒更新一次
  },

  // 页面卸载时清除定时器
  onUnload: function() {
    if (this.healthDataTimer) {
      clearInterval(this.healthDataTimer);
      this.healthDataTimer = null;
    }
  }
})
