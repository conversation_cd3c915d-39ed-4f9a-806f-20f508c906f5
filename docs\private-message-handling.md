# 站内私信消息处理功能

## 功能概述

在站内私信列表页面，当收到新的私信消息时，系统会根据发送者ID（senderId）自动更新对应的对话项。如果列表中没有对应的对话，则会新增一条对话记录。

## 实现逻辑

### 1. 消息数据结构

收到的私信消息格式：
```javascript
{
  content: "消息内容",
  createTime: "2025-07-01 15:45:16",
  id: "214",
  receiverId: "32",
  senderId: "33",
  status: "UNREAD",
  type: "text"
}
```

### 2. 处理流程

#### 2.1 更新未读数量
- 增加私信未读数量 (`unreadCounts.private_message`)
- 增加总未读数量 (`unreadCounts.total`)
- 更新底部tabBar的未读数量显示

#### 2.2 更新对话列表（仅在私信tab激活时）

**情况1：已存在对话**
- 根据 `senderId` 查找现有对话
- 更新对话的最新消息内容、时间和未读数量
- 将更新的对话移动到列表顶部

**情况2：新对话**
- 创建新的对话项，使用 `senderId` 作为唯一标识
- 尝试从消息中获取发送者信息（`senderName`、`senderAvatar`）
- 如果没有发送者信息，使用默认格式：`用户{senderId}`
- 将新对话添加到列表顶部
- 异步获取完整的用户信息并更新对话

#### 2.3 非激活状态处理
- 如果当前不在私信tab，标记需要刷新 (`needRefreshPrivateMessages = true`)
- 当用户切换到私信tab时会自动刷新列表

### 3. 用户信息获取

对于新对话，系统会尝试通过以下方式获取用户信息：
1. 从消息本身获取 `senderName` 和 `senderAvatar`
2. 异步调用 `messageApi.getMessageRecord(userId)` 获取聊天记录
3. 从聊天记录中提取用户信息并更新对话显示

### 4. 关键方法

#### `handlePrivateMessage(message)`
处理收到的私信消息的主要方法

#### `fetchUserInfoAndUpdateConversation(userId)`
异步获取用户信息并更新对话显示

## 测试功能

创建了测试页面 `pages/message-test/message-test` 用于验证功能：

### 测试场景
1. **新用户消息测试**：模拟收到新用户的私信，验证是否正确创建新对话
2. **已存在用户消息测试**：模拟收到已存在用户的私信，验证是否正确更新现有对话

### 使用方法
1. 先打开消息页面（`pages/messages/messages`）
2. 返回并打开测试页面（`pages/message-test/message-test`）
3. 点击相应的测试按钮
4. 返回消息页面查看效果

## 注意事项

1. **数据字段一致性**：确保使用正确的数据字段名（`privateMessages` 而非 `messages`）
2. **未读数量处理**：新消息会增加对话的 `countUnread` 字段
3. **时间格式化**：使用 `formatMessageTime` 方法统一格式化时间显示
4. **错误处理**：获取用户信息失败时使用默认信息，不影响基本功能
5. **性能考虑**：只在私信tab激活时才处理列表更新，减少不必要的操作

## 相关文件

- `pages/messages/messages.js` - 主要实现文件
- `pages/message-test/message-test.*` - 测试页面
- `api/messageApi.js` - 消息相关API
- `app.js` - WebSocket消息分发
