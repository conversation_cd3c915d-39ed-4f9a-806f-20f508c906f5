# 消息中心WebSocket消息处理功能

## 功能概述

在消息中心页面，当收到WebSocket推送的消息时，系统会自动处理不同类型的消息：
1. **站内私信**（subs_private_message）：根据发送者ID更新对话列表
2. **系统消息**（subs_sys_message）：刷新系统消息列表并更新未读数量
3. **通知公告**（subs_notice）：刷新通知公告列表并更新未读数量

## 实现逻辑

### 1. 消息数据结构

#### 私信消息格式：
```javascript
{
  content: "消息内容",
  createTime: "2025-07-01 15:45:16",
  id: "214",
  receiverId: "32",
  senderId: "33",
  status: "UNREAD",
  type: "text"
}
```

#### 系统消息格式：
```javascript
{
  id: "123",
  title: "系统消息标题",
  content: "系统消息内容",
  createTime: "2025-07-01 15:45:16",
  status: "UNREAD",
  type: "system"
}
```

#### 通知公告格式：
```javascript
{
  id: "456",
  title: "通知公告标题",
  content: "通知公告内容",
  createTime: "2025-07-01 15:45:16",
  read: false,
  type: "property_notice"
}
```

### 2. 处理流程

#### 2.1 私信消息处理

**更新未读数量**
- 增加私信未读数量 (`unreadCounts.private_message`)
- 增加总未读数量 (`unreadCounts.total`)
- 更新底部tabBar的未读数量显示

**更新对话列表（仅在私信tab激活时）**
- **已存在对话**：根据 `senderId` 查找现有对话，更新内容、时间和未读数量，移到列表顶部
- **新对话**：直接调用 `loadPrivateMessages()` 刷新整个私信列表

**非激活状态处理**
- 如果当前不在私信tab，标记需要刷新 (`needRefreshPrivateMessages = true`)

#### 2.2 系统消息处理

**更新未读数量**
- 增加系统消息未读数量 (`unreadCounts.system_message`)
- 增加总未读数量 (`unreadCounts.total`)
- 更新底部tabBar的未读数量显示

**刷新列表**
- 如果当前在系统消息tab，直接调用 `loadSystemMessages()` 刷新列表
- 如果不在系统消息tab，标记需要刷新 (`needRefreshSystemMessages = true`)
- 调用 `loadUnreadCounts()` 刷新未读数量统计

#### 2.3 通知公告处理

**更新未读数量**
- 增加通知公告未读数量 (`unreadCounts.notice_announcement`)
- 增加总未读数量 (`unreadCounts.total`)
- 更新底部tabBar的未读数量显示

**刷新列表**
- 如果当前在通知公告tab，直接调用 `loadNoticeMessages()` 刷新列表
- 如果不在通知公告tab，标记需要刷新 (`needRefreshNoticeMessages = true`)
- 调用 `loadUnreadCounts()` 刷新未读数量统计

### 3. 关键方法

#### `handlePrivateMessage(message)`
处理收到的私信消息的主要方法

#### `handleSystemMessage(message)`
处理收到的系统消息的主要方法

#### `handleNoticeMessage(message)`
处理收到的通知公告的主要方法

#### `loadPrivateMessages()`
刷新私信列表，获取完整的对话数据和用户信息

#### `loadSystemMessages()`
刷新系统消息列表

#### `loadNoticeMessages()`
刷新通知公告列表

#### `loadUnreadCounts()`
获取所有类型消息的未读数量统计

## 测试功能

创建了测试页面 `pages/message-test/message-test` 用于验证功能：

### 测试场景
1. **新用户私信测试**：模拟收到新用户的私信，验证是否正确刷新列表并显示新对话
2. **已存在用户私信测试**：模拟收到已存在用户的私信，验证是否正确更新现有对话
3. **系统消息测试**：模拟收到系统消息，验证是否正确刷新系统消息列表并更新未读数量
4. **通知公告测试**：模拟收到通知公告，验证是否正确刷新通知公告列表并更新未读数量

### 使用方法
1. 先打开消息页面（`pages/messages/messages`）
2. 返回并打开测试页面（`pages/message-test/message-test`）
3. 点击相应的测试按钮
4. 返回消息页面查看效果

## 注意事项

1. **数据字段一致性**：确保使用正确的数据字段名（`privateMessages` 而非 `messages`）
2. **未读数量处理**：新消息会增加对话的 `countUnread` 字段
3. **时间格式化**：使用 `formatMessageTime` 方法统一格式化时间显示
4. **列表刷新策略**：对于新对话，直接刷新整个列表确保数据完整性
5. **性能考虑**：只在私信tab激活时才处理列表更新，减少不必要的操作

## WebSocket主题配置

系统支持以下WebSocket消息主题：

| 主题 | 说明 | 处理方法 |
|------|------|----------|
| `subs_private_message` | 服务器推送的私信消息 | `handlePrivateMessage` |
| `subs_sys_message` | 服务器推送的系统消息 | `handleSystemMessage` |
| `subs_notice` | 服务器推送的通知公告 | `handleNoticeMessage` |
| `send_private_message` | 用户发送的私信 | `handlePrivateMessageReceived` |
| `send_private_message_response` | 私信发送响应 | `handlePrivateMessageResponse` |

## 相关文件

- `pages/messages/messages.js` - 主要实现文件
- `pages/message-test/message-test.*` - 测试页面
- `api/messageApi.js` - 消息相关API
- `app.js` - WebSocket消息分发
- `utils/websocket-config.js` - WebSocket配置
