// pages/property/workorder/process/index.js
const workOrderApi = require('@/api/workOrderApi.js');
const util = require('@/utils/util.js');

Page({
  data: {
    darkMode: false,
    workOrder: null,
    loading: true,
    submitting: false,

    // 页面模式：accept-受理工单
    mode: 'accept',

    // 员工列表
    staffList: [],
    selectedStaffIds: [], // 多选员工ID

    // 字典数据
    personWorkStatusDict: [], // 员工工作状态字典

    // 状态栏高度
    statusBarHeight: 20,
  },

  onLoad: function(options) {
    const { id, type } = options;

    // 获取状态栏高度
    const systemInfo = wx.getSystemInfoSync();
    const statusBarHeight = systemInfo.statusBarHeight;

    // 检查暗黑模式
    const app = getApp();
    if (app.globalData && app.globalData.darkMode !== undefined) {
      this.setData({
        darkMode: app.globalData.darkMode
      });
    }

    // 设置导航栏样式
    this.setData({
      statusBarHeight: statusBarHeight,
      processType: type || 'process'
    });

    // 设置导航栏标题
    let title = '处理工单';
    switch (this.data.processType) {
      case 'process':
        title = '接单处理';
        // 加载处理备注模板
        this.loadTemplates(templateManager.TEMPLATE_TYPES.PROCESS);
        break;
      case 'assign':
        title = '分配工单';
        this.loadStaffList();
        // 加载分配备注模板
        this.loadTemplates(templateManager.TEMPLATE_TYPES.ASSIGN);
        break;
      case 'complete':
        title = '完成工单';
        // 加载完成结果模板
        this.loadTemplates(templateManager.TEMPLATE_TYPES.COMPLETE);
        break;
      case 'record':
        title = '添加记录';
        // 加载处理记录模板
        this.loadTemplates(templateManager.TEMPLATE_TYPES.RECORD);
        break;
    }

    wx.setNavigationBarTitle({
      title: title
    });

    if (id) {
      this.loadWorkOrderDetail(id);
    } else {
      wx.showToast({
        title: '工单ID不存在',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 加载工单详情
  loadWorkOrderDetail: function(id) {
    this.setData({ loading: true });

    workOrderManager.getWorkOrderDetail(id)
      .then(order => {
        this.setData({
          workOrder: order,
          loading: false
        });
      })
      .catch(error => {
        console.error('加载工单详情失败', error);
        this.setData({ loading: false });

        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      });
  },

  // 加载员工列表
  loadStaffList: function() {
    // 这里应该调用API获取员工列表
    // 暂时使用模拟数据
    const mockStaffList = [
      { id: 'staff001', name: '张工', department: '工程部', position: '维修工程师' },
      { id: 'staff002', name: '李工', department: '工程部', position: '维修工程师' },
      { id: 'staff003', name: '王工', department: '工程部', position: '维修主管' }
    ];

    this.setData({ staffList: mockStaffList });
  },

  // 输入处理备注
  inputProcessRemark: function(e) {
    this.setData({ processRemark: e.detail.value });
  },

  // 选择员工
  selectStaff: function(e) {
    this.setData({ selectedStaffId: e.currentTarget.dataset.id });
  },

  // 输入分配备注
  inputAssignRemark: function(e) {
    this.setData({ assignRemark: e.detail.value });
  },

  // 输入完成结果
  inputCompleteResult: function(e) {
    this.setData({ completeResult: e.detail.value });
  },

  // 输入完成备注
  inputCompleteRemark: function(e) {
    this.setData({ completeRemark: e.detail.value });
  },

  // 上传完成图片
  uploadCompleteImages: function() {
    wx.chooseImage({
      count: 9 - this.data.completeImages.length,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        // 更新图片列表
        this.setData({
          completeImages: [...this.data.completeImages, ...res.tempFilePaths]
        });
      }
    });
  },

  // 删除完成图片
  removeCompleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.completeImages];
    images.splice(index, 1);
    this.setData({ completeImages: images });
  },

  // 输入记录动作
  inputRecordAction: function(e) {
    this.setData({ recordAction: e.detail.value });
  },

  // 输入记录备注
  inputRecordRemark: function(e) {
    this.setData({ recordRemark: e.detail.value });
  },

  // 设置表单错误
  setFormError: function(field, message) {
    const errors = { ...this.data.errors };
    errors[field] = message;
    this.setData({ errors });
  },

  // 清除表单错误
  clearFormErrors: function() {
    const errors = {
      processRemark: '',
      selectedStaffId: '',
      assignRemark: '',
      completeResult: '',
      completeRemark: '',
      recordAction: '',
      recordRemark: ''
    };
    this.setData({ errors });
  },

  // 验证表单
  validateForm: function() {
    const { processType, processRemark, selectedStaffId, completeResult, recordAction, recordRemark } = this.data;

    // 清除之前的错误信息
    this.clearFormErrors();

    // 根据处理类型创建验证模式
    let schema = {};

    if (processType === 'process') {
      // 接单处理验证
      schema = {
        processRemark: ['required']
      };

      // 验证表单
      const form = { processRemark };
      const result = formValidator.validateForm(form, schema);

      if (!result.valid) {
        formValidator.showFormErrors(result.errors, this.setFormError);
        formValidator.showFirstError(result.errors);
        return false;
      }
    } else if (processType === 'assign') {
      // 分配工单验证
      schema = {
        selectedStaffId: ['required']
      };

      // 验证表单
      const form = { selectedStaffId };
      const result = formValidator.validateForm(form, schema);

      if (!result.valid) {
        formValidator.showFormErrors(result.errors, this.setFormError);
        formValidator.showFirstError(result.errors);
        return false;
      }
    } else if (processType === 'complete') {
      // 完成工单验证
      schema = {
        completeResult: ['required']
      };

      // 验证表单
      const form = { completeResult };
      const result = formValidator.validateForm(form, schema);

      if (!result.valid) {
        formValidator.showFormErrors(result.errors, this.setFormError);
        formValidator.showFirstError(result.errors);
        return false;
      }
    } else if (processType === 'record') {
      // 添加记录验证
      schema = {
        recordAction: ['required'],
        recordRemark: ['required']
      };

      // 验证表单
      const form = { recordAction, recordRemark };
      const result = formValidator.validateForm(form, schema);

      if (!result.valid) {
        formValidator.showFormErrors(result.errors, this.setFormError);
        formValidator.showFirstError(result.errors);
        return false;
      }
    }

    return true;
  },

  // 提交处理
  submitProcess: function() {
    const { workOrder, processType, processRemark, selectedStaffId, staffList, completeResult, completeImages, completeRemark, recordAction, recordRemark } = this.data;

    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    this.setData({ submitting: true });

    // 根据处理类型调用不同的方法
    let promise;

    if (processType === 'process') {
      promise = workOrderManager.processOrder(workOrder.id, processRemark);
    } else if (processType === 'assign') {
      const selectedStaff = staffList.find(staff => staff.id === selectedStaffId);
      promise = workOrderManager.assignOrder(workOrder.id, selectedStaff, assignRemark);
    } else if (processType === 'complete') {
      promise = workOrderManager.completeOrder(workOrder.id, completeResult, completeImages, completeRemark);
    } else if (processType === 'record') {
      promise = workOrderManager.addProcessingRecord(workOrder.id, recordAction, recordRemark);
    }

    promise.then(() => {
      this.setData({ submitting: false });

      wx.showToast({
        title: '处理成功',
        icon: 'success'
      });

      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }).catch(error => {
      console.error('处理工单失败', error);
      this.setData({ submitting: false });

      wx.showToast({
        title: error.message || '处理失败，请重试',
        icon: 'none'
      });
    });
  },

  // 取消处理
  cancelProcess: function() {
    wx.navigateBack();
  },

  // 加载模板
  loadTemplates: function(type) {
    // 初始化模板数据
    templateManager.initTemplates();

    // 获取指定类型的模板
    const templates = templateManager.getTemplates(type);

    this.setData({ templates });
  },

  // 显示模板选择器
  showTemplateSelector: function() {
    this.setData({ showTemplateSelector: true });
  },

  // 隐藏模板选择器
  hideTemplateSelector: function() {
    this.setData({ showTemplateSelector: false });
  },

  // 选择模板
  selectTemplate: function(e) {
    const { index } = e.currentTarget.dataset;
    const template = this.data.templates[index];

    if (!template) return;

    // 根据处理类型设置不同的字段
    switch (this.data.processType) {
      case 'process':
        this.setData({
          processRemark: template.content,
          showTemplateSelector: false
        });
        break;
      case 'assign':
        this.setData({
          assignRemark: template.content,
          showTemplateSelector: false
        });
        break;
      case 'complete':
        this.setData({
          completeResult: template.content,
          showTemplateSelector: false
        });
        break;
      case 'record':
        this.setData({
          recordAction: template.content,
          recordRemark: template.remark || '',
          showTemplateSelector: false
        });
        break;
    }
  },

  // 导航到模板管理页面
  navigateToTemplates: function() {
    wx.navigateTo({
      url: '/pages/property/workorder/templates/index'
    });
  },

  // 导航返回
  navigateBack: function() {
    wx.navigateBack();
  }
});
